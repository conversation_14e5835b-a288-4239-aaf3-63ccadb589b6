<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Handyman Service User</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Handyman Service User</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.438524885554-8ljhtg9s08f65pekucqb5cdkfrq5d214</string>
			</array>
		</dict>
		<dict>
            <key>CFBundleURLName</key>
            <string>com.PhonePe-iOS-Intent-SDK-Integration</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>iOSIntentIntegration</string>
            </array>
        </dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-3940256099942544~1458002511</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>ppemerchantsdkv1</string>
		<string>ppemerchantsdkv2</string>
		<string>ppemerchantsdkv3</string>
		<string>paytmmp</string>
		<string>gpay</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Handyman Service app requires access to Apple Music in order to enhance your music experience within the app.</string>
	<key>NSCameraUsageDescription</key>
	<string>Handyman Service app requires access to your device's camera to allow you to upload your profile photo.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Handyman Service app requires access to your device's location to provide accurate service availability based on your current address.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Handyman Service app requires access to your location to provide location-based services and find nearby service providers.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Handyman Service app needs access to your device's location at all times to ensure seamless service delivery tailored to your precise location, even when the app is running in the background.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Handyman Service app requires access to your device's microphone when recording your speech using the "Mic" button.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
    <string>Handyman Service app requires access to Speech recognition when you will be used to determine which words you speak into this device microphone.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Handyman Service app requires access to your device's photo library to enable you to upload your profile photo.</string>
	<key>Privacy-Location Usage Description</key>
	<string>Handyman Service app requires access to your device's location to enhance your experience by providing personalized service recommendations and finding nearby service providers based on your current location.</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>cstr6suwn9.skadnetwork</string>
		</dict>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
