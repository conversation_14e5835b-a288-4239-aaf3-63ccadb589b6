plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.iqonic.servicebooking"
    compileSdkVersion 35
    ndkVersion = "27.0.12077973"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId 'com.iqonic.servicebooking'
        minSdkVersion 23
        targetSdkVersion 35
        versionCode 101
        versionName '11.13.2'
    }
    
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        debug  {
            shrinkResources false
            minifyEnabled false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }

        release {
            shrinkResources false
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation(platform("com.google.firebase:firebase-bom:32.4.0"))
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'phonepe.intentsdk.android.release:IntentSDK:2.3.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}