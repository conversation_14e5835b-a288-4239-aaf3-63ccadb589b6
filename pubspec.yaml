name: booking_system_flutter
description: A new Flutter project.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 11.13.2+101

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # COMMON
  nb_utils: ^7.1.4
  google_fonts: ^6.2.1
  html: ^0.15.5+1
  http: ^1.3.0
  intl: ^0.20.2
  cached_network_image: ^3.4.1
  url_launcher: ^6.3.1
  speech_to_text: ^7.0.0

  # FIREBASE
  firebase_core: ^3.13.0
  firebase_crashlytics: ^4.3.5
  firebase_auth: ^5.5.2
  firebase_storage: ^12.4.5
  cloud_firestore: ^5.6.6
  firebase_pagination: ^4.1.0

  # FIREBASE MESSAGING
  firebase_messaging: ^15.2.5
  flutter_local_notifications: ^19.1.0

  # STATE MANAGEMENT
  mobx: ^2.5.0
  flutter_mobx: ^2.3.0

  # ICON
  flutter_vector_icons: ^2.0.0
  flutter_svg: ^2.0.17

  # IMAGE
  image_picker: ^1.1.2
  photo_view: ^0.15.0
  lottie: ^3.3.1

  # OTHER
  country_picker: ^2.0.27
  flutter_custom_tabs: ^2.3.0
  permission_handler: ^12.0.0+1
  flutter_html: ^3.0.0-beta.2
  path_provider: ^2.1.5

  webview_flutter: ^4.10.0
  file_picker: ^10.1.2

  # SOCIAL LOGIN
  google_sign_in: ^6.3.0
  the_apple_sign_in: ^1.1.1

  # PAYMENT GATEWAYS
  midpay:
    git:
      url: https://github.com/iqonic-design/midpay.git
      ref: main  
  phonepe_payment_sdk: ^2.0.3
  qr_flutter: ^4.1.0 #required for in pix_flutter
  pix_flutter: ^2.2.0
  razorpay_flutter: ^1.4.0
  flutterwave_standard:
    git:
      url: https://github.com/iqonic-design/flutterwave_standard.git
      ref: main
  flutter_stripe: ^11.5.0
  cinetpay:
    git:
      url: https://github.com/iqonic-design/cinetpay.git
      ref: main
  flutter_paypal_checkout:
    git:
      url: https://github.com/iqonic-design/flutter_paypal_checkout.git
      ref: main

  flutter_paystack:
    git:
      url: https://github.com/iqonic-design/flutter_paystack.git
      ref: 1.1.0

  # MAP
  google_maps_flutter: ^2.12.1
  geolocator: ^13.0.4
  geocoding: ^3.0.0

  crypto: ^3.0.6 #required for in phonepe

  flutter_localizations:
    sdk: flutter
  carousel_slider: ^5.0.0
  share_plus: ^10.1.4

  dotted_border: ^2.1.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  mobx_codegen: ^2.7.0
  #flutter packages pub run build_runner build --delete-conflicting-outputs

flutter:
  uses-material-design: true
  assets:
    - assets/icons/
    - assets/
    - assets/flag/
    - assets/images/
    - assets/icons/upi_payment/
    - assets/lottie/
    - assets/newDashboard/
    - assets/json/


  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf