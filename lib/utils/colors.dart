import 'package:booking_system_flutter/utils/configs.dart';
import 'package:flutter/material.dart';

var primaryColor = defaultPrimaryColor;
const secondaryPrimaryColor = Color(0xfff3f4fa);
const lightPrimaryColor = Color(0xffebebf7);
const primaryLightColor = Color(0xFFEFEFF8);

//Text Color
const appTextPrimaryColor = Color(0xff1C1F34);
const appTextSecondaryColor = Color(0xff6C757D);
const cardColor = Color(0xFFF6F7F9);
const borderColor = Color(0xFFEBEBEB);

const scaffoldColorDark = Color(0xFF0E1116);
const scaffoldSecondaryDark = Color(0xFF1C1F26);
const appButtonColorDark = Color(0xFF282828);

const ratingBarColor = Color(0xfff5c609);
const verifyAcColor = Colors.blue;
const favouriteColor = Colors.red;
const unFavouriteColor = Colors.grey;
const lineTextColor = Color(0xFF6C757D);

//Status Color
const pending = Color(0xFFEA2F2F);
const accept = Color(0xFF00968A);
const on_going = Color(0xFFFD6922);
const in_progress = Color(0xFFB953C0);
const hold = Color(0xFFFFBD49);
const cancelled = Color(0xffFF0303);
const rejected = Color(0xFF8D0E06);
const failed = Color(0xFFC41520);
const completed = Color(0xFF3CAE5C);
const defaultStatus = Color(0xFF3CAE5C);
const pendingApprovalColor = Color(0xFF690AD3);
const waiting = Color(0xFF2CAFAF);

const add_booking = Color(0xFFEA2F2F);
const assigned_booking = Color(0xFFFD6922);
const transfer_booking = Color(0xFF00968A);
const update_booking_status = Color(0xFF3CAE5C);
const cancel_booking = Color(0xFFC41520);
const payment_message_status = Color(0xFFFFBD49);
const defaultActivityStatus = Color(0xFF3CAE5C);

const walletCardColor = Color(0xFF1C1E33);
const showRedForZeroRatingColor = Color(0xFFFA6565);

//Dashboard 3
const jobRequestComponentColor = Color(0xFFE4BB97);
const dashboard3CardColor = Color(0xFFF6F7F9);
const cancellationsBgColor = Color(0xFFFFE5E5);