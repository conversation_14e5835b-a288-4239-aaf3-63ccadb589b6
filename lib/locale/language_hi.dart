import 'package:booking_system_flutter/locale/languages.dart';

import '../utils/configs.dart';

class LanguageHi extends BaseLanguage {
  @override
  String get walkTitle1 => 'अपना खाता बनाएं और सेट करें';

  @override
  String get walkTitle2 => 'ब्राउज़ करें और सेवाएँ बुक करें';

  @override
  String get walkTitle3 => 'अपनी बुकिंग को ट्रैक और प्रबंधित करें';

  @override
  String get getStarted => 'शुरू हो जाओ';

  @override
  String get signIn => 'साइन इन करें';

  @override
  String get signUp => 'साइन अप करें';

  @override
  String get hintFirstNameTxt => 'अपना पहला नाम दर्ज करें';

  @override
  String get hintLastNameTxt => 'अपना अंतिम नाम दर्ज करें';

  @override
  String get hintContactNumberTxt => 'अपना संपर्क नंबर दर्ज करें';

  @override
  String get hintEmailAddressTxt => 'अपना ईमेल पता दर्ज करें';

  @override
  String get hintUserNameTxt => 'उपयोगकर्ता नाम';

  @override
  String get hintPasswordTxt => 'अपना कूटशब्द भरें';

  @override
  String get hintReenterPasswordTxt => 'दुबारापासवडृ िलखो';

  @override
  String get confirm => 'पुष्टि करना';

  @override
  String get hintEmailTxt => 'अपना ईमेल दर्ज करें';

  @override
  String get forgotPassword => 'पासवर्ड भूल गए?';

  @override
  String get alreadyHaveAccountTxt => 'क्या आपके पास पहले से एक खाता मौजूद है';

  @override
  String get rememberMe => 'मुझे याद रखें';

  @override
  String get resetPassword => 'पासवर्ड रीसेट';

  @override
  String get dashboard => 'डैशबोर्ड';

  @override
  String get editProfile => 'प्रोफ़ाइल संपादित करें';

  @override
  String get camera => 'कैमरा';

  @override
  String get language => 'भाषा';

  @override
  String get appTheme => 'ऐप थीम';

  @override
  String get bookingHistory => 'बुकिंग इतिहास';

  @override
  String get rateUs => 'हमें रेटिंग दें';

  @override
  String get termsCondition => 'नियम व शर्त';

  @override
  String get helpSupport => 'मदद समर्थन';

  @override
  String get privacyPolicy => 'गोपनीयता नीति';

  @override
  String get about => 'के बारे में';

  @override
  String get logout => 'लॉग आउट';

  @override
  String get chooseTheme => 'ऐप थीम चुनें';

  @override
  String get selectCountry => 'देश चुनें';

  @override
  String get selectState => 'राज्य चुनें';

  @override
  String get selectCity => 'शहर चुनें';

  @override
  String get changePassword => 'पासवर्ड बदलें';

  @override
  String get passwordNotMatch => "पासवर्ड मैच नहीं कर रहा है";

  @override
  String get doNotHaveAccount => "खाता नहीं है?";

  @override
  String get hintNewPasswordTxt => "अपना नया पासवर्ड दर्ज करें";

  @override
  String get hintOldPasswordTxt => "अपना पुराना पासवर्ड दर्ज करें";

  @override
  String get hintAddress => 'अपना पता दर्ज करें';

  @override
  String get lblGallery => "गेलरी";

  @override
  String get yourReview => "आपकी समीक्षा";

  @override
  String get review => "समीक्षा";

  @override
  String get hintDescription => "अपना विवरण दर्ज करें";

  @override
  String get lblApply => "लागू करना";

  @override
  String get bookTheService => "सेवा बुक करें";

  @override
  String get contactAdmin => "कृपया व्यवस्थापक से संपर्क करें";

  @override
  String get allServices => 'सभी सेवाएं';

  @override
  String get duration => 'अवधि';

  @override
  String get hourly => "प्रति घंटा";

  @override
  String get payment => "भुगतान";

  @override
  String get done => "किया हुआ";

  @override
  String get totalAmount => 'कुल राशि';

  @override
  String get applyCoupon => 'कूपन लागू करें';

  @override
  String get priceDetail => 'मूल्य विवरण';

  @override
  String get home => 'घर';

  @override
  String get category => 'श्रेणी';

  @override
  String get booking => 'बुकिंग';

  @override
  String get profile => 'प्रोफ़ाइल';

  @override
  String get lblAlertBooking => 'क्या आप सेवा बुक करना चाहते हैं?';

  @override
  String get serviceName => 'सेवा का नाम';

  @override
  String get service => 'सेवा';

  @override
  String get lblCancelReason => 'कृपया इस सेवा बुकिंग को रद्द करने का कारण दर्ज करें।';

  @override
  String get enterReason => "यहां कारण दर्ज करें";

  @override
  String get noDataAvailable => 'कोई डेटा उपलब्ध नहीं है';

  @override
  String get lblOk => 'ठीक';

  @override
  String get paymentDetail => 'भुगतान विवरण';

  @override
  String get paymentStatus => 'भुगतान की स्थिति';

  @override
  String get viewDetail => 'विस्तार से देखें';

  @override
  String get appThemeLight => 'रोशनी';

  @override
  String get appThemeDark => 'अंधेरा';

  @override
  String get appThemeDefault => 'प्रणालीगत चूक';

  @override
  String get markAsRead => "सभी को पढ़ा हुआ मार्क करें";

  @override
  String get lblYes => 'हां';

  @override
  String get lblNo => 'नहीं';

  @override
  String get btnRate => "अब रेट करें";

  @override
  String get btnSubmit => "प्रस्तुत करना";

  @override
  String get walkThrough1 => 'अपने ईमेल या सोशल मीडिया प्रोफाइल का उपयोग करके अपने खाते में साइन अप करें या लॉग इन करें। अपना प्रोफ़ाइल पूरा करने से एक सहज बुकिंग अनुभव सुनिश्चित होता है।';

  @override
  String get walkThrough2 => 'अपने क्षेत्र में उपलब्ध सेवाओं की विस्तृत श्रृंखला का अन्वेषण करें। किसी सेवा का चयन करें, एक सुविधाजनक समय स्लॉट चुनें और किसी सेवा को जल्दी और आसानी से बुक करने के लिए अपना स्थान विवरण प्रदान करें।';

  @override
  String get walkThrough3 => 'वास्तविक समय में अपनी सेवा की स्थिति पर नज़र रखें। अपनी वर्तमान और पिछली बुकिंग देखें और प्रबंधित करें। आगामी सेवाओं को सहजता से पुनर्निर्धारित या रद्द करें।';

  @override
  String get lblNotification => "अधिसूचनाएं";

  @override
  String get lblUnAuthorized => "डेमो उपयोगकर्ता को इस क्रिया के लिए नहीं दिया जा सकता है";

  @override
  String get btnNext => "अगला";

  @override
  String get lblViewAll => "सभी को देखें";

  @override
  String get notAvailable => "नहीं हैहै";

  @override
  String get lblFavorite => "पसंदीदा सेवाएं";

  @override
  String get lblChat => "चैट";

  @override
  String get getLocation => "स्थान प्राप्त करें";

  @override
  String get setAddress => "पता लगाना";

  @override
  String get requiredText => "यह फ़ील्ड आवश्यक है";

  @override
  String get phnRequiredText => "कृपया मोबाइल नंबर दर्ज करें";

  @override
  String get lblCall => "बुलाना";

  @override
  String get lblRateHandyman => "रेट हैंडमैन";

  @override
  String get msgForLocationOn => 'आपका स्थान चालू है। सभी क्षेत्रों से उपलब्ध सेवाओं को देखना जारी रखें?';

  @override
  String get msgForLocationOff => 'आपका स्थान बंद हो गया है। अपने चयनित क्षेत्र में उपलब्ध सेवाओं की खोज और खोजें।';

  @override
  String get lblEnterPhnNumber => "अपना फोन नंबर डालें";

  @override
  String get btnSendOtp => "ओटीपी भेजें";

  @override
  String get lblLocationOff => "सभी सेवाएं उपलब्ध हैं";

  @override
  String get lblAppSetting => "ऐप सेटिंग";

  @override
  String get lblSubTotal => "कर राशि";

  @override
  String get lblImage => "छवि";

  @override
  String get lblVideo => "वीडियो";

  @override
  String get lblAudio => "ऑडियो";

  @override
  String get lblChangePwdTitle => "आपका नया पासवर्ड पिछले उपयोग किए गए पासवर्ड से अलग होना चाहिए";

  @override
  String get lblForgotPwdSubtitle => "एक रीसेट पासवर्ड लिंक ऊपर दर्ज किए गए ईमेल पते पर भेजा जाएगा";

  @override
  String get lblLoginTitle => "फिर से हैलो";

  @override
  String get lblLoginSubTitle => "आपका स्वागत है, आप लंबे समय से चूक गए हैं";

  @override
  String get lblOrContinueWith => "या के साथ जारी रखें";

  @override
  String get lblHelloUser => "हैलो उपयोगकर्ता!";

  @override
  String get lblSignUpSubTitle => 'बेहतर अनुभव के लिए अपना खाता बनाएं';

  @override
  String get lblStepper1Title => "विस्तार जानकारी दर्ज करें";

  @override
  String get lblDateAndTime => "तिथि और समय:";

  @override
  String get chooseDateAndTime => 'दिनांक और समय चुनें';

  @override
  String get lblYourAddress => "आपका पता";

  @override
  String get lblEnterYourAddress => "अपना पता दर्ज करें";

  @override
  String get lblUseCurrentLocation => "वर्तमान स्थान का उपयोग करें";

  @override
  String get lblEnterDescription => "विवरण दर्ज करें";

  @override
  String get lblPrice => "कीमत";

  @override
  String get lblTax => "कर";

  @override
  String get lblDiscount => "छूट";

  @override
  String get lblAvailableCoupons => "उपलब्ध कूपन";

  @override
  String get lblPrevious => "पहले का";

  @override
  String get lblCoupon => "कूपन";

  @override
  String get lblEditYourReview => "अपनी समीक्षा संपादित करें";

  @override
  String get lblTime => "समय";

  @override
  String get textProvider => "प्रदाता";

  @override
  String get lblConfirmBooking => "बुकिंग की पुष्टि करें";

  @override
  String get lblConfirmMsg => "क्या आप वाकई इस बुकिंग की पुष्टि करना चाहते हैं?";

  @override
  String get lblCancel => "रद्द करें";

  @override
  String get lblExpiryDate => "समाप्ति तिथि :";

  @override
  String get lblRemoveCoupon => "कूपन निकालें";

  @override
  String get lblNoCouponsAvailable => "कोई कूपन उपलब्ध नहीं है";

  @override
  String get lblStep1 => "चरण 1";

  @override
  String get lblStep2 => "चरण 2";

  @override
  String get lblBookingID => "बुकिंग आईडी";

  @override
  String get lblDate => "तारीख";

  @override
  String get lblAboutHandyman => "हैंन्डमैन के बारे में";

  @override
  String get lblAboutProvider => "प्रदाता के बारे में";

  @override
  String get lblNotRatedYet => "आपने अभी तक रेट नहीं किया है";

  @override
  String get lblDeleteReview => "समीक्षा हटाएं";

  @override
  String get lblConfirmReviewSubTitle => 'क्या आप यह समीक्षा हटाना चाहते हैं?';

  @override
  String get lblConfirmService => 'क्या आप यह सेवा रखना चाहते हैं?';

  @override
  String get lblConFirmResumeService => 'क्या आप इस सेवा को फिर से शुरू करना चाहते हैं?';

  @override
  String get lblEndServicesMsg => "क्या आप इस सेवा को समाप्त करना चाहते हैं?";

  @override
  String get lblCancelBooking => "बुकिंग रद्द करें";

  @override
  String get lblStart => "शुरू";

  @override
  String get lblHold => "पकड़";

  @override
  String get lblResume => "फिर शुरू करना";

  @override
  String get lblPayNow => "अब भुगतान करें";

  @override
  String get lblCheckStatus => "अवस्था जांच";

  @override
  String get lblID => "पहचान";

  @override
  String get lblNoBookingsFound => "कोई बुकिंग नहीं मिली";

  @override
  String get lblCategory => "श्रेणी";

  @override
  String get lblYourComment => "आपकी टिप्पणियां";

  @override
  String get lblIntroducingCustomerRating => "ग्राहक रेटिंग पेश करना";

  @override
  String get lblSeeYourRatings => "अपनी रेटिंग देखें";

  @override
  String get lblFeatured => "विशेष रुप से प्रदर्शित";

  @override
  String get lblNoServicesFound => "कोई सेवाएं नहीं मिली";

  @override
  String get lblGENERAL => "आम";

  @override
  String get lblAboutApp => "ऐप के बारे में";

  @override
  String get lblPurchaseCode => "पूर्ण स्रोत कोड खरीदें";

  @override
  String get lblNoRateYet => "वर्तमान में आपने किसी भी सेवा को रेट नहीं किया है";

  @override
  String get lblMemberSince => "से सदस्य";

  @override
  String get lblFilterBy => "फिल्टर के द्वारा";

  @override
  String get lblClearFilter => "स्पष्ट निस्यंदक";

  @override
  String get lblNoReviews => "कोई समीक्षा नहीं";

  @override
  String get lblUnreadNotification => "अपठित अधिसूचना";

  @override
  String get lblChoosePaymentMethod => "भुगतान विधि चुनें";

  @override
  String get lblNoPayments => "कोई भुगतान नहीं";

  @override
  String get lblPayWith => "क्या आप के साथ भुगतान करना चाहते हैं";

  @override
  String get payWith => "के साथ भुगतान करें";

  @override
  String get lblYourRating => "तुम्हारी रेटिंग";

  @override
  String get lblEnterReview => "अपनी समीक्षा दर्ज करें (वैकल्पिक)";

  @override
  String get lblDelete => "हटाएं";

  @override
  String get lblDeleteRatingMsg => 'क्या आप यह रेटिंग हटाना चाहते हैं?';

  @override
  String get lblSelectRating => 'रेटिंग की आवश्यकता है';

  @override
  String get lblNoServiceRatings => "कोई सेवा रेटिंग नहीं";

  @override
  String get lblSearchFor => "निम्न को खोजें";

  @override
  String get lblRating => "रेटिंग";

  @override
  String get lblAvailableAt => "पर उपलब्ध";

  @override
  String get lblRelatedServices => "संबंधित सेवाएं";

  @override
  String get lblBookNow => "अभी बुक करें";

  @override
  String get lblWelcomeToHandyman => "$APP_NAME में आपका स्वागत है";

  @override
  String get lblWalkThroughSubTitle => "पूर्ण समाधान के साथ $APP_NAME सेवा - ऑन-डिमांड होम सर्विसेज ऐप";

  @override
  String get textHandyman => "हैंडमैन";

  @override
  String get lblChooseFromMap => "मानचित्र से चुनें";

  @override
  String get lblDeleteAddress => "पता हटाएं";

  @override
  String get lblDeleteSunTitle => 'क्या आप यह पता हटाना चाहते हैं?';

  @override
  String get lblFaq => "पूछे जाने वाले प्रश्न";

  @override
  String get lblServiceFaq => "सेवा सामान्य प्रश्न";

  @override
  String get lblLogoutTitle => "अरे नहीं, आप जा रहे हैं!";

  @override
  String get lblLogoutSubTitle => "क्या आप लॉगआउट करना चाहते हैं?";

  @override
  String get lblFeaturedProduct => "यह चित्रित उत्पाद है";

  @override
  String get lblAlert => "चेतावनी";

  @override
  String get lblOnBase => "के आधार पर";

  @override
  String get lblInvalidCoupon => "कूपन कोड अमान्य है";

  @override
  String get lblSelectCode => "कृपया कूपन कोड का चयन करें";

  @override
  String get lblBackPressMsg => "ऐप से बाहर निकलने के लिए फिर से दबाएं";

  @override
  String get lblHour => "घंटा";

  @override
  String get lblHelplineNumber => "हेल्पलाइन नंबर";

  @override
  String get lblSubcategories => "उपश्रेणियों";

  @override
  String get lblAgree => "मैं इसके लिए सहमत हूँ";

  @override
  String get lblTermsOfService => "सेवा की शर्तें";

  @override
  String get lblWalkThrough0 => "अप्रेंटिस सेवा - पूर्ण समाधान के साथ ऑन -डिमांड होम सर्विसेज ऐप";

  @override
  String get lblServiceTotalTime => "सेवा कुल समय";

  @override
  String get lblDateTimeUpdated => 'आपकी बुकिंग की तारीख और समय सफलतापूर्वक पूरा हो गया है';

  @override
  String get lblSelectDate => "कृपया दिनांक समय का चयन करें";

  @override
  String get lblReasonCancelling => "कारण:";

  @override
  String get lblReasonRejecting => "इस बुकिंग को अस्वीकार करने का कारण";

  @override
  String get lblFailed => "कारण यह बुकिंग विफल क्यों है";

  @override
  String get lblNotDescription => "कोई वर्णन उपलब्ध नहीं";

  @override
  String get lblMaterialTheme => "सामग्री आप थीम सक्षम करें";

  @override
  String get lblServiceProof => "सेवा प्रमाण";

  @override
  String get lblAndroid12Support => "यह कार्रवाई आपके ऐप को पुनरारंभ कर देगी। पुष्टि करना?";

  @override
  String get lblOff => "छूट";

  @override
  String get lblHr => "घंटा";

  @override
  String get lblSignInWithGoogle => "गूगल के साथ साइन इन करें";

  @override
  String get lblSignInWithOTP => "ओटीपी के साथ साइन इन करें";

  @override
  String get lblDangerZone => "खतरा क्षेत्र";

  @override
  String get lblDeleteAccount => "खाता हटा दो";

  @override
  String get lblUnderMaintenance => "रखरखाव जारी...";

  @override
  String get lblCatchUpAfterAWhile => "थोड़ी देर के बाद पकड़ो";

  @override
  String get lblId => "पहचान";

  @override
  String get lblMethod => "तरीका";

  @override
  String get lblStatus => "दर्जा";

  @override
  String get lblPending => "लंबित";

  @override
  String get confirmationRequestTxt => 'क्या आप यह क्रिया करना चाहते हैं?';

  @override
  String get lblDeleteAccountConformation => "आपका खाता स्थायी रूप से हटा दिया जाएगा। आपका डेटा फिर से बहाल नहीं किया जाएगा।";

  @override
  String get lblAutoSliderStatus => "ऑटो स्लाइडर स्थिति";

  @override
  String get lblPickAddress => "पिक पता";

  @override
  String get lblUpdateDateAndTime => "अद्यतन दिनांक और समय";

  @override
  String get lblRecheck => "पुनः जांच करें";

  @override
  String get lblLoginAgain => "कृपया फिर भाग लें";

  @override
  String get lblUpdate => "अद्यतन";

  @override
  String get lblNewUpdate => "नई अपडेट";

  @override
  String get lblOptionalUpdateNotify => "वैकल्पिक अद्यतन सूचित करें";

  @override
  String get lblAnUpdateTo => "के लिए एक अद्यतन";

  @override
  String get lblIsAvailableWouldYouLike => "उपलब्ध है। क्या आप अद्यतन करना चाहते हैं?";

  @override
  String get lblRegisterAsPartner => "भागीदार के रूप में पंजीकृत करें";

  @override
  String get lblSignInWithApple => "Apple के साथ साइन इन करें";

  @override
  String get lblWaitingForProviderApproval => "प्रदाता अनुमोदन की प्रतीक्षा में";

  @override
  String get lblFree => "अवैतनिक";

  @override
  String get lblAppleSignInNotAvailable => "एप्पल साइनइन आपके डिवाइस के लिए उपलब्ध नहीं है";

  @override
  String get lblTotalExtraCharges => "कुल अतिरिक्त प्रभार";

  @override
  String get lblWaitingForResponse => "प्रतिक्रिया के लिए प्रतीक्षा कर रहा हूँ";

  @override
  String get lblAll => "सभी";

  @override
  String get noConversation => "कोई बातचीत नहीं";

  @override
  String get noConversationSubTitle => "आपने अभी तक कोई बातचीत नहीं की है। किसी प्रदाता के साथ चैट करने के लिए एक सेवा बुक करें।";

  @override
  String get noBookingSubTitle => "लगता है कि आपने अभी तक अपना ऑर्डर बुक नहीं किया है";

  @override
  String get myReviews => "मेरी समीक्षा";

  @override
  String get noCategoryFound => "कोई श्रेणी नहीं मिली";

  @override
  String get noProviderFound => "कोई प्रदाता नहीं मिला";

  @override
  String get createServiceRequest => "सेवा अनुरोध बनाएं";

  @override
  String get chooseImages => "चित्र चुनें";

  @override
  String get serviceDescription => "सेवा विवरण";

  @override
  String get addNewService => "नई सेवा जोड़ें";

  @override
  String get newPostJobRequest => 'नई नौकरी का अनुरोध पोस्ट करें';

  @override
  String get postJobTitle => "नौकरी का शीर्षक";

  @override
  String get postJobDescription => "नौकरी विवरण पोस्ट";

  @override
  String get services => "सेवाएं";

  @override
  String get myPostJobList => "मेरी कस्टम जॉब लिस्ट";

  @override
  String get requestNewJob => "नई नौकरी का अनुरोध करें";

  @override
  String get noNotifications => "कोई सूचनाएं नहीं";

  @override
  String get noNotificationsSubTitle => "एक बार आपके पास कुछ होने के बाद हम आपको सूचित करेंगे";

  @override
  String get noFavouriteSubTitle => "आपकी पसंदीदा सेवाएँ यहाँ दिखाई देंगी";

  @override
  String get termsConditionsAccept => "कृपया नियम और शर्तें स्वीकार करें";

  @override
  String get disclaimer => "अस्वीकरण";

  @override
  String get disclaimerContent => "आपकी बुकिंग पूरी होने के बाद आपसे भुगतान के लिए कहा जाएगा।";

  @override
  String get inputMustBeNumberOrDigit => 'इनपुट संख्या या अंक होना चाहिए';

  @override
  String get requiredAfterCountryCode => 'देश कोड के बाद आवश्यक';

  @override
  String get selectedOtherBookingTime => 'चयनित बुकिंग समय पहले ही पारित हो चुका है। कृपया दूसरी बार चुनें।';

  @override
  String get myServices => 'मेरी सेवाएं';

  @override
  String get doYouWantToAssign => 'क्या आप असाइन करना चाहते हैं';

  @override
  String get bidPrice => 'दाम लगाना';

  @override
  String get accept => 'स्वीकार करना';

  @override
  String get price => 'कीमत';

  @override
  String get remove => 'हटाना';

  @override
  String get add => 'जोड़ें';

  @override
  String get save => 'सहेजें';

  @override
  String get createPostJobWithoutSelectService => 'आप सेवा का चयन किए बिना पोस्ट जॉब नहीं बना सकते';

  @override
  String get selectCategory => 'श्रेणी चुनना';

  @override
  String get pleaseAddImage => 'कृपया छवि जोड़ें';

  @override
  String get selectedBookingTimeIsAlreadyPassed => 'चयनित बुकिंग समय पहले ही पारित हो चुका है। कृपया दूसरी बार चुनें।';

  @override
  String get jobPrice => 'नौकरी की कीमत';

  @override
  String get estimatedPrice => 'अनुमानित मूल्य';

  @override
  String get bidder => 'बोलीदाता';

  @override
  String get assignedProvider => 'सौंपा प्रदाता';

  @override
  String get myPostDetail => 'मेरी पोस्ट विवरण';

  @override
  String get thankYou => 'शुक्रिया!';

  @override
  String get bookingConfirmedMsg => 'आपकी बुकिंग की पुष्टि की जाती है।';

  @override
  String get goToHome => 'मुखपृष्ठ प्र जाएं';

  @override
  String get goToReview => 'समीक्षा पर जाएं';

  @override
  String get noServiceAdded => 'कोई सेवा नहीं जोड़ी';

  @override
  String get noPostJobFound => 'कोई पोस्ट जॉब नहीं मिला';

  @override
  String get noPostJobFoundSubtitle => 'जब आप अपनी नौकरी पोस्ट करते हैं, तो प्रत्येक प्रदाता सूचित करेगा, और आप काम पूरा करने के लिए अपने वांछित प्रदाता का चयन कर सकते हैं।';

  @override
  String get pleaseEnterValidOTP => 'कृपया मान्य OTP दर्ज करें';

  @override
  String get confirmOTP => 'ओटीपी की पुष्टि करें';

  @override
  String get sendingOTP => 'ओटीपी भेजना';

  @override
  String get pleaseSelectDifferentSlotThenPrevious => '""कृपया अलग -अलग स्लॉट का चयन करें तो पिछले';

  @override
  String get pleaseSelectTheSlotsFirst => 'कृपया पहले स्लॉट चुनें';

  @override
  String get editTimeSlotsBooking => 'समय स्लॉट बुकिंग संपादित करें';

  @override
  String get availableSlots => 'उपलब्ध स्लॉट';

  @override
  String get noTimeSlots => 'कोई समय स्लॉट नहीं';

  @override
  String get bookingDateAndSlot => 'ऊकिंग तिथि और स्लॉट';

  @override
  String get extraCharges => 'अतिरिक्त शुल्क';

  @override
  String get chatCleared => 'चैट क्लीयर';

  @override
  String get clearChat => 'यह स्पष्ट है कि';

  @override
  String get jobRequestSubtitle => 'आईडी आपकी सेवा नहीं मिली? चिंता न करें, आप अपनी आवश्यकताओं को पोस्ट कर सकते हैं।';

  @override
  String get verified => 'सत्यापित';

  @override
  String get theEnteredCodeIsInvalidPleaseTryAgain => 'दर्ज कोड अमान्य है, कृपया पुनः प्रयास करें';

  @override
  String get otpCodeIsSentToYourMobileNumber => 'ओटीपी कोड आपके मोबाइल नंबर पर भेजा जाता है';

  @override
  String get yourPaymentFailedPleaseTryAgain => 'आपका भुगतान विफल हो गया कृपया पुनः प्रयास करें';

  @override
  String get yourPaymentHasBeenMadeSuccessfully => 'आपका भुगतान सफलतापूर्वक किया गया है';

  @override
  String get transactionFailed => 'लेन - देन विफल';

  @override
  String get lblStep3 => 'चरण 3';

  @override
  String get lblAvailableOnTheseDays => 'इन दिनों पर उपलब्ध है';

  @override
  String get internetNotAvailable => 'आपका इंटरनेट ऑफ़लाइन प्रतीत होता है';

  @override
  String get pleaseTryAgain => 'कृपया पुन: प्रयास करें';

  @override
  String get somethingWentWrong => 'कुछ गलत हो गया';

  @override
  String get postJob => 'पोस्ट जॉब';

  @override
  String get package => 'पैकेट';

  @override
  String get frequentlyBoughtTogether => 'अक्सर एक साथ खरीदे जाने वाले';

  @override
  String get endOn => 'पर समाप्त होता है';

  @override
  String get buy => 'खरीदना';

  @override
  String get includedServices => 'शामिल सेवाएँ';

  @override
  String get includedInThisPackage => 'इस पैकेज में शामिल है';

  @override
  String get lblInvalidTransaction => 'अवैध लेन - देन';

  @override
  String get getTheseServiceWithThisPackage => 'आप इस पैकेज के साथ ये सेवाएं प्राप्त करेंगे';

  @override
  String get lblNotValidUser => 'आप एक वैध उपयोगकर्ता नहीं हैं';

  @override
  String get lblSkip => 'छोडना';

  @override
  String get lblChangeCountry => 'देश बदलें';

  @override
  String get lblTimeSlotNotAvailable => 'यह स्लॉट उपलब्ध नहीं है';

  @override
  String get lblAdd => 'जोड़ना';

  @override
  String get lblThisService => 'जै सेवा';

  @override
  String get lblYourCurrenciesNotSupport => 'आपकी मुद्राएं CinetPay का समर्थन नहीं करती हैं';

  @override
  String get lblSignInFailed => 'भाग लेना विफल हुआ';

  @override
  String get lblUserCancelled => 'उपयोगकर्ता रद्द कर दिया';

  @override
  String get lblTransactionCancelled => 'रद्द किया गया लेनदेन';

  @override
  String get lblExample => 'उदाहरण';

  @override
  String get lblCheckOutWithCinetPay => 'CinetPay के साथ चेकआउट';

  @override
  String get lblLocationPermissionDenied => 'स्थान की अनुमति से इनकार किया जाता है।';

  @override
  String get lblLocationPermissionDeniedPermanently => 'स्थान की अनुमति को स्थायी रूप से अस्वीकार कर दिया जाता है, हम अनुमतियों का अनुरोध नहीं कर सकते।';

  @override
  String get lblEnableLocation => 'कृपया सुनिश्चित करें कि स्थान सेवाएं सक्षम हैं।';

  @override
  String get lblNoUserFound => 'कोई उपयोगकर्ता नहीं मिला';

  @override
  String get lblUserNotCreated => 'उपयोगकर्ता नहीं बनाया गया';

  @override
  String get lblTokenExpired => 'टोकन समाप्त हो गया';

  @override
  String get lblConfirmationForDeleteMsg => 'क्या आप संदेश हटाना चाहते हैं?';

  @override
  String get favouriteProvider => 'पसंदीदा प्रदाता';

  @override
  String get noProviderFoundMessage => 'आपके पसंदीदा प्रदाता यहां दिखाई देंगे';

  @override
  String get personalInfo => 'व्यक्तिगत जानकारी';

  @override
  String get essentialSkills => 'आवश्यक कौशल';

  @override
  String get knownLanguages => 'ज्ञात भाषाएँ';

  @override
  String get authorBy => 'द्वारा लेखक';

  @override
  String get views => 'दृश्य';

  @override
  String get blogs => 'ब्लॉग';

  @override
  String get noBlogsFound => 'कोई ब्लॉग नहीं मिला';

  @override
  String get requestInvoice => 'इनवॉयस के लिए अनुरोध करो';

  @override
  String get invoiceSubTitle => 'ईमेल पता दर्ज करें जहां आप अपना चालान प्राप्त करना चाहते हैं';

  @override
  String get sentInvoiceText => 'कृपया अपना ईमेल देखें हमने आपके ईमेल पर चालान भेजा है।';

  @override
  String get send => 'भेजना';

  @override
  String get published => 'प्रकाशित';

  @override
  String get clearChatMessage => 'क्या आप इस चैट को साफ़ करना चाहते हैं?';

  @override
  String get deleteMessage => 'क्या आप हटाना चाहते हैं?';

  @override
  String get accepted => 'को स्वीकृत';

  @override
  String get onGoing => 'चल रहे';

  @override
  String get inProgress => 'चालू';

  @override
  String get cancelled => 'रद्द';

  @override
  String get rejected => 'अस्वीकार कर दिया';

  @override
  String get failed => 'असफल';

  @override
  String get completed => 'पुरा होना।';

  @override
  String get pendingApproval => 'लंबित अनुमोदन';

  @override
  String get waiting => 'इंतज़ार में';

  @override
  String get paid => 'चुकाया गया';

  @override
  String get advancePaid => 'अग्रिम भुगतान';

  @override
  String get insufficientBalanceMessage => 'आपके बटुए में एक अपर्याप्त संतुलन है। कृपया एक और विधि चुनें।';

  @override
  String get cinetPayNotSupportedMessage => 'CinetPay आपकी मुद्राओं द्वारा समर्थित नहीं है';

  @override
  String get loading => 'लोड हो रहा है..';

  @override
  String get walletBalance => 'बटुआ शेष';

  @override
  String get payAdvance => 'अदायगी';

  @override
  String get advancePaymentMessage => 'बुकिंग को पूरा करने के लिए अग्रिम भुगतान करें';

  @override
  String get advancePayAmount => 'अग्रिम वेतन राशि';

  @override
  String get remainingAmount => 'बाकी अमाउंट';

  @override
  String get advancePayment => 'अग्रिम भुगतान';

  @override
  String get withExtraAndAdvanceCharge => 'अतिरिक्त शुल्क और अग्रिम भुगतान के साथ';

  @override
  String get withExtraCharge => 'अतिरिक्त शुल्क के साथ';

  @override
  String get min => 'मिन';

  @override
  String get hour => 'घंटा';

  @override
  String get customerRatingMessage => 'दूसरों को बताएं कि आप क्या सोचते हैं';

  @override
  String get paymentHistory => 'भुगतान इतिहास';

  @override
  String get message => 'संदेश';

  @override
  String get wallet => 'बटुआ';

  @override
  String get payWithFlutterWave => 'फ्लूटवेव के साथ भुगतान करें';

  @override
  String get goodMorning => 'शुभ प्रभात';

  @override
  String get goodAfternoon => 'नमस्कार';

  @override
  String get goodEvening => 'नमस्ते';

  @override
  String get invalidURL => 'असामान्य यूआरएल';

  @override
  String get use24HourFormat => '24-घंटे के प्रारूप का प्रयोग करें?';

  @override
  String get email => 'ईमेल';

  @override
  String get badRequest => 'गलत अनुरोध';

  @override
  String get forbidden => 'निषिद्ध';

  @override
  String get pageNotFound => 'पृष्ठ नहीं मिला';

  @override
  String get tooManyRequests => 'बहुत सारे अनुरोध';

  @override
  String get internalServerError => 'आंतरिक सर्वर त्रुटि';

  @override
  String get badGateway => 'खराब गेटवे';

  @override
  String get serviceUnavailable => 'सेवा उपलब्ध नहीं';

  @override
  String get gatewayTimeout => 'गेटवे समय समाप्त';

  @override
  String get pleaseWait => 'कृपया प्रतीक्षा करें';

  @override
  String get externalWallet => 'बाह्य बटुआ';

  @override
  String get userNotFound => 'उपयोगकर्ता नहीं मिला';

  @override
  String get requested => 'का अनुरोध किया';

  @override
  String get assigned => 'सौंपा गया';

  @override
  String get reload => 'पुनः लोड करें';

  @override
  String get lblStripeTestCredential => 'परीक्षण क्रेडेंशियल अधिक भुगतान नहीं कर सकता है तो 500';

  @override
  String get noDataFoundInFilter => 'सर्वोत्तम परिणाम प्राप्त करने के लिए सबसे अच्छा फ़िल्टर मानदंड चुनें';

  @override
  String get addYourCountryCode => 'अपना देश कोड जोड़ें';

  @override
  String get help => 'मदद';

  @override
  String get couponCantApplied => 'यह कूपन लागू नहीं किया जा सकता है';

  @override
  String get priceAmountValidationMessage => 'मूल्य राशि 0 से अधिक होनी चाहिए';

  @override
  String get pleaseWaitWhileWeLoadChatDetails => 'कृपया प्रतीक्षा करें जब हम चैट विवरण लोड करते हैं';

  @override
  String get isNotAvailableForChat => 'चैट के लिए उपलब्ध नहीं है';

  @override
  String get connectWithFirebaseForChat => 'चैट के लिए फायरबेस के साथ कनेक्ट करें';

  @override
  String get closeApp => 'बंद अनुप्रयोग';

  @override
  String get providerAddedToFavourite => 'प्रदाता को पसंदीदा सूची में जोड़ा गया';

  @override
  String get providerRemovedFromFavourite => 'प्रदाता को पसंदीदा सूची से हटा दिया गया';

  @override
  String get provideValidCurrentPasswordMessage => 'आपको एक मान्य वर्तमान पासवर्ड प्रदान करना होगा';

  @override
  String get copied => 'कॉपी किया गया';

  @override
  String get copyMessage => 'प्रतिलिपि संदेश';

  @override
  String get messageDelete => 'संदेश को हटाएं';

  @override
  String get pleaseChooseAnyOnePayment => 'कृपया कोई एक भुगतान विधि चुनें';

  @override
  String get myWallet => 'मेरा बटुआ';

  @override
  String get balance => 'संतुलन';

  @override
  String get topUpWallet => 'टॉप-अप वॉलेट';

  @override
  String get topUpAmountQuestion => 'आप किस राशि के साथ टॉप करना पसंद करेंगे?';

  @override
  String get paymentMethod => 'भुगतान विधि';

  @override
  String get selectYourPaymentMethodToAddBalance => 'शेष जोड़ने के लिए अपनी भुगतान विधि का चयन करें';

  @override
  String get proceedToTopUp => 'टॉप-अप के लिए आगे बढ़ें';

  @override
  String get serviceAddedToFavourite => 'पसंदीदा सूची में जोड़ा गया सेवा';

  @override
  String get serviceRemovedFromFavourite => 'पसंदीदा सूची से हटा दी गई सेवा';

  @override
  String get firebaseRemoteCannotBe => 'फायरबेस रिमोट को कनेक्ट नहीं किया जा सकता है';

  @override
  String get search => 'खोज';

  @override
  String get close => 'बंद करना';

  @override
  String get totalAmountShouldBeMoreThan => 'कुल राशि से अधिक होना चाहिए';

  @override
  String get totalAmountShouldBeLessThan => 'कुल राशि से कम होनी चाहिए';

  @override
  String get doYouWantToTopUpYourWallet => 'क्या आप अब अपने बटुए को ऊपर करना चाहते हैं?';

  @override
  String get chooseYourLocation => 'अपना स्थान चुनें';

  @override
  String get connect => 'जोड़ना';

  @override
  String get transactionId => 'लेन -देन आईडी';

  @override
  String get at => 'पर';

  @override
  String get appliedTaxes => 'अनुप्रयुक्त कर';

  @override
  String get accessDeniedContactYourAdmin => 'पहुंच अस्वीकृत। सहायता के लिए अपने व्यवस्थापक से संपर्क करें।';

  @override
  String get yourWalletIsUpdated => 'आपका बटुआ अपडेट किया गया है!';

  @override
  String get by => 'द्वारा';

  @override
  String get noPaymentMethodFound => 'कोई भुगतान विधि नहीं मिली';

  @override
  String get theAmountShouldBeEntered => 'राशि दर्ज की जानी चाहिए';

  @override
  String get walletHistory => 'बटुए का इतिहास';

  @override
  String get debit => 'खर्चे में लिखना';

  @override
  String get credit => 'श्रेय';

  @override
  String get youCannotApplyThisCoupon => 'आप इस कूपन को लागू नहीं कर सकते';

  @override
  String get basedOn => 'पर आधारित';

  @override
  String get serviceStatusPicMessage => 'कृपया सुनिश्चित करें कि आप कम से कम एक बुकिंग की स्थिति चुनें';

  @override
  String get clearFilter => 'स्पष्ट निस्यंदक';

  @override
  String get bookingStatus => 'बुकिंग स्थिति';

  @override
  String get addOns => 'ऐड-ऑन';

  @override
  String get serviceAddOns => 'सेवा जोड़ें';

  @override
  String get turnOn => 'चालू करो';

  @override
  String get turnOff => 'बंद करें';

  @override
  String get serviceVisitType => 'सेवा यात्रा प्रकार';

  @override
  String get thisServiceIsOnlineRemote => 'यह सेवा ऑनलाइन/दूरस्थ रूप से पूरी हो जाएगी।';

  @override
  String get deleteMessageForAddOnService => 'क्या आप इस ऐड-ऑन सेवा को हटाना चाहते हैं?';

  @override
  String get confirmation => 'पुष्टि!';

  @override
  String get pleaseNoteThatAllServiceMarkedCompleted => 'कृपया ध्यान दें कि सभी सेवा ऐड-ऑन को पूरा किया गया है!';

  @override
  String get writeHere => 'यहाँ लिखें';

  @override
  String get isAvailableGoTo => 'उपलब्ध है। प्ले स्टोर पर जाएं और ऐप का नया संस्करण डाउनलोड करें।';

  @override
  String get later => 'बाद में';

  @override
  String get whyChooseMe => 'मुझे क्यों चुनें?';

  @override
  String get useThisCodeToGet => 'प्राप्त करने के लिए इस कोड का उपयोग करें';

  @override
  String get off => 'बंद';

  @override
  String get applied => 'लागू';

  @override
  String get coupons => 'कूपन';

  @override
  String get handymanList => 'अप्रत्यक्ष सूची';

  @override
  String get noHandymanFound => 'कोई अप्रेंटिस नहीं मिला';

  @override
  String get back => 'पीछे';

  @override
  String get team => 'टीम';

  @override
  String get whyChooseMeAs => 'मुझे अपने विश्वसनीय सेवा प्रदाता के रूप में क्यों चुनें';

  @override
  String get reason => 'कारण';

  @override
  String get pleaseEnterAddressAnd => 'कृपया पता और बुकिंग दिनांक और स्लॉट दर्ज करें';

  @override
  String get pleaseEnterYourAddress => 'कृपया अपना पता दर्ज करें';

  @override
  String get pleaseSelectBookingDate => 'कृपया बुकिंग दिनांक और स्लॉट चुनें';

  @override
  String get doYouWantTo => 'क्या आप इस कूपन को हटाना चाहते हैं?';

  @override
  String get chooseDateTime => 'दिनांक और समय चुनें';

  @override
  String get airtelMoneyPayment => 'एयरटेल मुद्रा भुगतान';

  @override
  String get recommendedForYou => 'आप के लिए अनुशंसित';

  @override
  String get paymentSuccess => 'भुगतान की सफलता';

  @override
  String get redirectingToBookings => 'बुकिंग के लिए पुनर्निर्देशन ..';

  @override
  String get transactionIsInProcess => 'लेनदेन प्रक्रिया में है ...';

  @override
  String get pleaseCheckThePayment => 'कृपया जाँच करें कि भुगतान अनुरोध अपने नंबर पर भेजा गया है';

  @override
  String get enterYourMsisdnHere => 'यहां अपना MSISDN दर्ज करें';

  @override
  String get theTransactionIsStill => 'लेन -देन अभी भी प्रसंस्करण है और अस्पष्ट स्थिति में है। कृपया लेनदेन की स्थिति लाने के लिए लेनदेन की जांच करें।';

  @override
  String get transactionIsSuccessful => 'लेनदेन सफल है';

  @override
  String get incorrectPinHasBeen => 'गलत पिन दर्ज किया गया है';

  @override
  String get theUserHasExceeded => 'उपयोगकर्ता ने अपने बटुए की अनुमति लेनदेन सीमा को पार कर लिया है';

  @override
  String get theAmountUserIs => 'उपयोगकर्ता द्वारा स्थानांतरित करने की कोशिश कर रहा है, अनुमत न्यूनतम राशि से कम है';

  @override
  String get userDidnTEnterThePin => 'उपयोगकर्ता ने पिन दर्ज नहीं किया';

  @override
  String get transactionInPendingState => 'लंबित राज्य में लेनदेन। कृपया कुछ समय बाद जाँच करें';

  @override
  String get userWalletDoesNot => 'उपयोगकर्ता वॉलेट के पास देय राशि को कवर करने के लिए पर्याप्त पैसा नहीं है';

  @override
  String get theTransactionWasRefused => 'लेन -देन से इनकार कर दिया गया था';

  @override
  String get thisIsAGeneric => 'यह एक सामान्य इनकार है जिसके कई संभावित कारण हैं';

  @override
  String get payeeIsAlreadyInitiated => 'PAYEE को पहले से ही मंथन या वर्जित के लिए शुरू किया गया है या एयरटेल मनी प्लेटफॉर्म पर पंजीकृत नहीं है';

  @override
  String get theTransactionWasTimed => 'लेन -देन का समय समाप्त हो गया था।';

  @override
  String get theTransactionWasNot => 'लेन -देन नहीं मिला।';

  @override
  String get xSignatureAndPayloadDid => 'एक्स-हस्ताक्षर और पेलोड से मेल नहीं खाती';

  @override
  String get encryptionKeyHasBeen => 'एन्क्रिप्शन कुंजी को सफलतापूर्वक लाया गया है';

  @override
  String get couldNotFetchEncryption => 'एन्क्रिप्शन कुंजी नहीं ला सका';

  @override
  String get transactionHasBeenExpired => 'लेनदेन समाप्त हो गया है';

  @override
  String get ambiguous => 'अस्पष्ट';

  @override
  String get success => 'सफलता';

  @override
  String get incorrectPin => 'गलत पिन';

  @override
  String get exceedsWithdrawalAmountLimitS => 'निकासी राशि सीमा (ओं) / निकासी राशि सीमा से अधिक हो जाती है';

  @override
  String get invalidAmount => 'अवैध राशि';

  @override
  String get transactionIdIsInvalid => 'लेन -देन आईडी अमान्य है';

  @override
  String get inProcess => 'प्रक्रिया में';

  @override
  String get notEnoughBalance => 'पर्याप्त संतुलन नहीं है';

  @override
  String get refused => 'अस्वीकार करना';

  @override
  String get doNotHonor => 'सम्मान मत कर';

  @override
  String get transactionNotPermittedTo => 'लेनदेन को भुगतान करने की अनुमति नहीं है';

  @override
  String get transactionTimedOut => 'लेन -देन समाप्त हो गया';

  @override
  String get transactionNotFound => 'लेन -देन नहीं मिला';

  @override
  String get forBidden => 'निषिद्ध';

  @override
  String get successfullyFetchedEncryptionKey => 'सफलतापूर्वक एन्क्रिप्शन कुंजी प्राप्त की';

  @override
  String get errorWhileFetchingEncryption => 'एन्क्रिप्शन कुंजी प्राप्त करते समय त्रुटि';

  @override
  String get transactionExpired => 'लेन -देन समाप्त हो गया';

  @override
  String get verifyEmail => 'ईमेल सत्यापित करें';

  @override
  String get minRead => 'मिनट पढ़ें';

  @override
  String get loadingChats => 'लोडिंग चैट ...';

  @override
  String get monthly => 'महीने के';

  @override
  String get noCouponsAvailableMsg => 'इस समय कोई कूपन नहीं। अनन्य ऑफ़र के लिए वापस जाँच करते रहें!';

  @override
  String get refundPolicy => 'भुगतान वापसी की नीति';

  @override
  String get chooseAnyOnePayment => 'पहले कोई भी भुगतान विधि चुनें';

  @override
  String get january => 'जनवरी';

  @override
  String get february => 'फ़रवरी';

  @override
  String get march => 'मार्च';

  @override
  String get april => 'अप्रैल';

  @override
  String get may => 'मई';

  @override
  String get june => 'जून';

  @override
  String get july => 'जुलाई';

  @override
  String get august => 'अगस्त';

  @override
  String get september => 'सितम्बर';

  @override
  String get october => 'अक्टूबर';

  @override
  String get november => 'नवंबर';

  @override
  String get december => 'दिसंबर';

  @override
  String get monthName => 'माह का नाम';

  @override
  String get mon => 'सोमवार';

  @override
  String get tue => 'मंगल';

  @override
  String get wed => 'बुध';

  @override
  String get thu => 'गुरु';

  @override
  String get fri => 'शुक्र';

  @override
  String get sat => 'बैठा';

  @override
  String get sun => 'रवि';

  @override
  String get weekName => 'सप्ताह का नाम';

  @override
  String get removeThisFile => 'इस फ़ाइल को हटाएँ';

  @override
  String get areYouSureWantToRemoveThisFile => 'क्या आप इस फ़ाइल को हटाना चाहते हैं?';

  @override
  String get sendMessage => 'मेसेज भेजें';

  @override
  String get youAreNotConnectedWithChatServer => 'चैट सर्वर से कनेक्ट करें';

  @override
  String get NotConnectedWithChatServerMessage => 'आप चैट सर्वर से कनेक्ट नहीं हैं. कनेक्ट करने और चैटिंग शुरू करने के लिए नीचे दिए गए बटन पर टैप करें';

  @override
  String get sentYouAMessage => 'आपको एक संदेश भेजा';

  @override
  String get pushNotification => 'सर्वर पुश नोटीफिकेशन';

  @override
  String get yourBooking => 'आपकी बुकिंग';

  @override
  String get featuredServices => 'विशेष रुप से सेवाएं';

  @override
  String get postYourRequestAnd => 'अपना अनुरोध पोस्ट करें, और हम इसे पूरा करने की पूरी कोशिश करेंगे';

  @override
  String get newRequest => 'नई विनती';

  @override
  String get upcomingBooking => 'आगामी बुकिंग';

  @override
  String get theUserHasDenied => 'उपयोगकर्ता ने वाक् पहचान के उपयोग से इनकार किया है';

  @override
  String get helloGuest => 'नमस्ते अतिथि';

  @override
  String get eGCleaningPlumberPest => 'जैसे सफाई, प्लंबर, कीट नियंत्रण';

  @override
  String get ifYouDidnTFind => 'यदि आपको हमारी सेवा नहीं मिली, तो चिंता न करें! आप आसानी से अपना अनुरोध पोस्ट कर सकते हैं.';

  @override
  String get popularServices => 'लोकप्रिय सेवाएँ';

  @override
  String get canTFindYourServices => 'आपकी सेवाएँ नहीं मिल पा रही हैं?';

  @override
  String get trackProviderLocation => 'ट्रैक प्रदाता स्थान';

  @override
  String get trackHandymanLocation => 'अप्रेंटिस स्थान को ट्रैक करें';

  @override
  String get handymanLocation => 'सहायक स्थान';

  @override
  String get providerLocation => 'प्रदाता स्थान';

  @override
  String get lastUpdatedAt => 'अंतिम बार अद्यतन किया गया:';

  @override
  String get track => 'रास्ता';

  @override
  String get handymanReached => 'अप्रेंटिस पहुंच गया? शुरू करने के लिए क्लिक करें';

  @override
  String get providerReached => 'प्रदाता तक पहुंच गया? शुरू करने के लिए क्लिक करें';

  @override
  String get lblBankDetails => "बैंक विवरण";

  @override
  String get addBank => "बैंक जोड़ें";

  @override
  String get bankList => "बैंक सूची";

  @override
  String get lbldefault => "गलती करना";

  @override
  String get setAsDefault => "डिफाल्ट के रूप में सेट";

  @override
  String get aadharNumber => "आधार नंबर";

  @override
  String get panNumber => "पैन नंबर";

  @override
  String get lblPleaseEnterAccountNumber => "कृपया खाता संख्या दर्ज करें";

  @override
  String get lblAccountNumberMustContainOnlyDigits => "खाता संख्या में केवल अंक ही होने चाहिए";

  @override
  String get lblAccountNumberMustBetween11And16Digits => "खाता संख्या 11 से 16 अंकों के बीच होनी चाहिए";

  @override
  String get noBankDataTitle => "कोई बैंक डेटा नहीं मिला";

  @override
  String get noBankDataSubTitle => "आपने अभी तक बैंक नहीं जोड़ा है";

  @override
  String get active => 'सक्रिय';

  @override
  String get inactive => 'निष्क्रिय';

  @override
  String get deleteBankTitle => 'क्या आप इस बैंक को हटाना चाहते हैं?';

  @override
  String get bankName => 'बैंक का नाम';

  @override
  String get withdraw => "बैंक विवरण";

  @override
  String get accountNumber => 'खाता संख्या';

  @override
  String get iFSCCode => 'आईएफएससी कोड';

  @override
  String get lblEdit => 'संपादित करें';

  @override
  String get availableBalance => "उपलब्ध शेष राशि";

  @override
  String get successful => 'सफल';

  @override
  String get yourWithdrawalRequestHasBeenSuccessfullySubmitted => 'आपका निकासी अनुरोध सफलतापूर्वक सबमिट कर दिया गया है।';

  @override
  String get eg3000 => 'जैसे" 3000"';

  @override
  String get chooseBank => "बैंक चुनें";

  @override
  String get egCentralNationalBank => 'जैसे" केंद्रीय राष्ट्रीय बैंक"';

  @override
  String get topUp => "लबालब भरना";

  @override
  String get withdrawRequest => "अनुरोध वापस लें";

  @override
  String get lblEnterAmount => "राशि डालें";

  @override
  String get pleaseAddLessThanOrEqualTo => "कृपया इससे कम या इसके बराबर जोड़ें";

  @override
  String get btnSave => 'सहेजें';

  @override
  String get fullNameOnBankAccount => 'बैंक खाते पर पूरा नाम';

  @override
  String get packageIsExpired => 'पैकेज समाप्त हो गया है';

  @override
  String get bookPackage => 'बुक पैकेज';

  @override
  String get packageDescription => 'पैकेज विवरण';

  @override
  String get packagePrice => 'पैकेज मूल्य';

  @override
  String get online => 'ऑनलाइन';

  @override
  String get noteAddressIsNot => 'नोट: दूरस्थ सेवाओं के लिए पता आवश्यक नहीं है।';

  @override
  String get wouldYouLikeTo => 'क्या आप आगे बढ़ना और इस बुकिंग की पुष्टि करना चाहेंगे?';

  @override
  String get packageName => 'पैकेज का नाम';

  @override
  String get feeAppliesForCancellations => 'शुल्क भीतर किए गए रद्दीकरण के लिए लागू होता है';

  @override
  String get a => 'ए';

  @override
  String get byConfirmingYouAgree => 'पुष्टि करके, आप हमारी बात से सहमत हैं';

  @override
  String get and => 'और';

  @override
  String get areYouSureYou => 'आप आप रद्द करना चाहते हैं? आपकी सेवा कीमत के आधार पर रद्दीकरण शुल्क लागू हो सकता है';

  @override
  String get totalCancellationFee => 'कुल रद्दीकरण शुल्क';

  @override
  String get goBack => 'वापस जाओ';

  @override
  String get bookingCancelled => 'बुकिंग रद्द';

  @override
  String get yourBookingHasBeen => 'आपकी बुकिंग सफलतापूर्वक रद्द कर दी गई है. लागू रिफंड 24 घंटे के भीतर संसाधित किया जाएगा';

  @override
  String get noteCheckYourBooking => 'नोट: रिफंड विवरण के लिए अपना बुकिंग इतिहास जांचें';

  @override
  String get cancelledReason => 'रद्द कारण';

  @override
  String get refundPaymentDetails => 'रिफंड भुगतान विवरण';

  @override
  String get refundOf => 'का रिफंड';

  @override
  String get refundAmount => 'वापसी राशि';

  @override
  String get cancellationFee => 'रद्दीकरण शुल्क';

  @override
  String get advancedPayment => 'उन्नत भुगतान';

  @override
  String get hoursOfTheScheduled => 'निर्धारित सेवा के घंटे';

  @override
  String get open => 'खुला';

  @override
  String get closed => 'बंद किया हुआ';

  @override
  String get createBy => 'द्वारा बनाएं';

  @override
  String get repliedBy => 'द्वारा उत्तर दिया गया';

  @override
  String get closedBy => 'द्वारा बंद कर दिया गया';

  @override
  String get helpDesk => 'सहायता केंद्र';

  @override
  String get addNew => 'नया जोड़ो';

  @override
  String get queryYet => 'फिर भी प्रश्न करें';

  @override
  String get toSubmitYourProblems => 'अपनी समस्याएं प्रस्तुत करने के लिए बस ऐड बटन दबाएं और अपनी चिंता बताएं';

  @override
  String get noRecordsFoundFor => 'इसका कोई रिकार्ड नहीं मिला';

  @override
  String get queries => 'प्रश्न.';

  @override
  String get noActivityYet => 'अभी तक कोई गतिविधि नहीं';

  @override
  String get noRecordsFound => 'कोई रिकॉर्ड नहीं मिला';

  @override
  String get reply => 'जवाब';

  @override
  String get eGDuringTheService => 'जैसे सेवा के दौरान, फर्नीचर गलती से क्षतिग्रस्त हो गया था।';

  @override
  String get doYouWantClosedThisQuery => 'क्या आप इस क्वेरी को बंद करना चाहते हैं';

  @override
  String get markAsClosed => 'बंद के रूप में चिह्नित करें';

  @override
  String get youCanMarkThis => 'यदि आप हमारे उत्तर से संतुष्ट हैं तो आप इसे बंद के रूप में चिह्नित कर सकते हैं';

  @override
  String get subject => 'विषय';

  @override
  String get eGDamagedFurniture => 'जैसे क्षतिग्रस्त फर्नीचर';

  @override
  String get closedOn => 'पर बंद:';

  @override
  String get on => 'पर';

  @override
  String get showMessage => 'संदेश दिखाएँ';

  @override
  String get yesterday => 'कल';

  @override
  String get chooseAction => 'कार्रवाई का चयन';

  @override
  String get chooseImage => 'छवि चुनें';

  @override
  String get noteYouCanUpload => 'नोट: आप छवि को \'jpg\', \'png\', \'jpeg\' एक्सटेंशन के साथ अपलोड कर सकते हैं और आप केवल एक छवि का चयन कर सकते हैं';

  @override
  String get removeImage => 'छवि हटाएँ';

  @override
  String get advancedRefund => 'उन्नत धनवापसी';

  @override
  String get lblService => 'सेवा';

  @override
  String get dateRange => 'दिनांक सीमा';

  @override
  String get paymentType => 'भुगतान प्रकार';

  @override
  String get reset => 'रीसेट करें';

  @override
  String get noStatusFound => 'कोई स्थिति नहीं मिली';

  @override
  String get selectStartDateEndDate => 'आरंभ तिथि और समाप्ति तिथि चुनें';

  @override
  String get handymanNotFound => 'सहायक नहीं मिला';

  @override
  String get providerNotFound => 'प्रदाता नहीं मिला';

  @override
  String get rateYourExperience => 'अपने अनुभव को रेटिंग दें';

  @override
  String get weValueYourFeedback => 'हम आपकी प्रतिक्रिया को महत्व देते हैं! कृपया हमारी सेवा के साथ अपने हालिया अनुभव को रेटिंग दें';

  @override
  String get viewStatus => 'स्थिति देखें';

  @override
  String get paymentInfo => 'भुगतान जानकारी';

  @override
  String get mobile => 'गतिमान:';

  @override
  String get to => 'को';

  @override
  String get chooseYourDateRange => 'अपनी दिनांक सीमा चुनें';

  @override
  String get asHandyman => 'अप्रेंटिस के रूप में';

  @override
  String get passwordLengthShouldBe => 'पासवर्ड की लंबाई 8 से 12 अक्षर होनी चाहिए।';

  @override
  String get cash => 'नकद';

  @override
  String get bank => 'बैंक';

  @override
  String get razorPay => "रेज़रपे";

  @override
  String get payPal => "पेपल";

  @override
  String get stripe => "स्ट्राइप";

  @override
  String get payStack => "पे स्टैक";

  @override
  String get flutterWave => "फ्लटरवेव";

  @override
  String get paytm => "पेटीएम";

  @override
  String get airtelMoney => "एयरटेल मनी";

  @override
  String get cinet => "सिनेट";

  @override
  String get midtrans => "मिडट्रांस";

  @override
  String get sadadPayment => "सदाद";

  @override
  String get phonePe => "फोनपे";

  @override
  String get inAppPurchase => "इन-ऐप परचेज़";

  @override
  String get pix => "पिक्स";

  @override
  String get chooseWithdrawalMethod => "निकासी विधि चुनें";
}