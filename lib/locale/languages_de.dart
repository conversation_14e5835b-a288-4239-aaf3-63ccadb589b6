import '../utils/configs.dart';
import 'languages.dart';

class LanguageDe extends BaseLanguage {
  @override
  String get walkTitle1 => '<PERSON><PERSON><PERSON>n und richten Sie Ihr Konto ein';

  @override
  String get walkTitle2 => 'Durchsuchen und buchen Sie Dienste';

  @override
  String get walkTitle3 => 'Verfolgen und verwalten Sie Ihre Buchungen';

  @override
  String get getStarted => "Loslegen";

  @override
  String get signIn => "Einloggen";

  @override
  String get signUp => "Anmeldung";

  @override
  String get hintFirstNameTxt => "Geben Sie Ihren Vornamen ein";

  @override
  String get hintLastNameTxt => "Geben Sie Ihren Nachnamen ein";

  @override
  String get hintContactNumberTxt => "Geben Sie Ihre Kontaktnummer ein";

  @override
  String get hintEmailAddressTxt => "Geben sie ihre E-Mailadresse ein";

  @override
  String get hintUserNameTxt => 'Nutzername';

  @override
  String get hintPasswordTxt => "Geben Sie Ihr Passwort ein";

  @override
  String get hintReenterPasswordTxt => "Wiederhole die Eingabe deines Passwortes";

  @override
  String get confirm => "Bestätigen Sie";

  @override
  String get hintEmailTxt => "Geben sie ihre E-Mail Adresse ein";

  @override
  String get forgotPassword => "Passwort vergessen?";

  @override
  String get alreadyHaveAccountTxt => "Sie haben bereits ein Konto?";

  @override
  String get rememberMe => "Erinnere dich an mich";

  @override
  String get resetPassword => "Passwort zurücksetzen";

  @override
  String get dashboard => "Armaturenbrett";

  @override
  String get editProfile => "Profil bearbeiten";

  @override
  String get camera => "Kamera";

  @override
  String get language => "Sprache";

  @override
  String get appTheme => "App-Thema.";

  @override
  String get bookingHistory => "Buchungsverlauf";

  @override
  String get rateUs => "Bewerten Sie uns";

  @override
  String get termsCondition => "Allgemeine Geschäftsbedingungen";

  @override
  String get helpSupport => "Hilfe Unterstützung";

  @override
  String get privacyPolicy => "Datenschutz-Bestimmungen";

  @override
  String get about => "Über";

  @override
  String get logout => "Ausloggen";

  @override
  String get chooseTheme => "Wählen Sie das App-Thema";

  @override
  String get selectCountry => "Land auswählen";

  @override
  String get selectState => "Staat wählen";

  @override
  String get selectCity => "Stadt wählen";

  @override
  String get changePassword => "Passwort ändern";

  @override
  String get passwordNotMatch => "Passwort stimmt nicht überein";

  @override
  String get doNotHaveAccount => "Ich habe kein Konto?";

  @override
  String get hintNewPasswordTxt => "Gib dein neues Passwort ein";

  @override
  String get hintOldPasswordTxt => "Geben Sie Ihr altes Passwort ein";

  @override
  String get hintAddress => "Geben Sie Ihre Adresse ein";

  @override
  String get lblGallery => "Galerie";

  @override
  String get yourReview => "Deine Bewertung";

  @override
  String get review => "Rezensionen";

  @override
  String get hintDescription => "Geben Sie Ihre Beschreibung ein";

  @override
  String get lblApply => "Anwenden";

  @override
  String get bookTheService => "Buchen Sie den Service.";

  @override
  String get contactAdmin => "Bitte wenden Sie sich an den Administrator";

  @override
  String get allServices => "Alle dienstleistungen.";

  @override
  String get duration => "Dauer";

  @override
  String get hourly => "stündlich";

  @override
  String get payment => "Zahlung";

  @override
  String get done => "Fertig";

  @override
  String get totalAmount => "Gesamtsumme";

  @override
  String get applyCoupon => "Gutschein anwenden.";

  @override
  String get priceDetail => "Preisdetails.";

  @override
  String get home => "Heim";

  @override
  String get category => "Kategorien";

  @override
  String get booking => "Buchung";

  @override
  String get profile => "Profil";

  @override
  String get lblAlertBooking => 'Möchten Sie den Service buchen?';

  @override
  String get serviceName => "Dienstname";

  @override
  String get service => "Dienstleistungen";

  @override
  String get lblCancelReason => "Bitte geben Sie den Grund ein, um diese Buchung zu stornieren.";

  @override
  String get enterReason => "Grund hier angeben";

  @override
  String get noDataAvailable => "Keine Daten verfügbar";

  @override
  String get lblOk => "Okay";

  @override
  String get paymentDetail => "Zahlungsdetails";

  @override
  String get paymentStatus => "Zahlungsstatus";

  @override
  String get viewDetail => "Im Detail sehen";

  @override
  String get appThemeLight => "Hell";

  @override
  String get appThemeDark => "Dunkel";

  @override
  String get appThemeDefault => "Systemfehler";

  @override
  String get markAsRead => "Markiere alle als gelesen";

  @override
  String get lblYes => "Jawohl";

  @override
  String get lblNo => "Nein";

  @override
  String get btnRate => "Jetzt bewerten";

  @override
  String get btnSubmit => "einreichen";

  @override
  String get walkThrough1 => 'Registrieren Sie sich oder melden Sie sich mit Ihren E-Mail- oder Social-Media-Profilen bei Ihrem Konto an. Die Vervollständigung Ihres Profils sorgt für ein reibungsloses Buchungserlebnis.';

  @override
  String get walkThrough2 => 'Entdecken Sie die große Auswahl an Dienstleistungen in Ihrer Nähe. Wählen Sie einen Dienst aus, wählen Sie ein passendes Zeitfenster und geben Sie Ihre Standortdaten an, um schnell und einfach einen Dienst zu buchen.';

  @override
  String get walkThrough3 => 'Verfolgen Sie Ihren Servicestatus in Echtzeit. Sehen und verwalten Sie Ihre aktuellen und vergangenen Buchungen. Verschieben oder stornieren Sie anstehende Dienste mühelos.';

  @override
  String get lblNotification => "Benachrichtigungen";

  @override
  String get lblUnAuthorized => "Demo-Benutzer kann nicht für diese Aktion gewährt werden";

  @override
  String get btnNext => "Nächste";

  @override
  String get lblViewAll => "Alle ansehen";

  @override
  String get notAvailable => "Nicht verfügbar";

  @override
  String get lblFavorite => "Lieblingsdienste";

  @override
  String get lblChat => "Plaudern";

  @override
  String get getLocation => "Standort erhalten";

  @override
  String get setAddress => "Adresse einstellen";

  @override
  String get requiredText => "Trường này là bắt buộc";

  @override
  String get phnRequiredText => "Vui lòng nhập số điện thoại di động";

  @override
  String get lblCall => "Anruf";

  @override
  String get lblRateHandyman => "Bewerten Sie Handwerker";

  @override
  String get msgForLocationOn => 'Ihr Standort ist eingeschaltet. Sehen Sie sich die Dienste fort, die in allen Bereichen verfügbar sind?';

  @override
  String get msgForLocationOff => 'Ihr Standort ist ausgeschaltet. Entdecken und finden Sie in Ihrem ausgewählten Bereich zur Verfügung stehenden Dienste.';

  @override
  String get lblEnterPhnNumber => "Trage deine Telefonnummer ein";

  @override
  String get btnSendOtp => "Senden Sie OTP.";

  @override
  String get lblLocationOff => "Alle Dienstleistungen verfügbar.";

  @override
  String get lblAppSetting => "App-Einstellung.";

  @override
  String get lblSubTotal => "Steuerbetrag";

  @override
  String get lblImage => "Bild";

  @override
  String get lblVideo => "Video";

  @override
  String get lblAudio => "Audio";

  @override
  String get lblChangePwdTitle => "Ihr neues Passwort muss sich von dem vorherigen verwendeten Kennwort unterscheiden";

  @override
  String get lblForgotPwdSubtitle => "Eine RESET-Kennwortverbindung wird an die oben eingegebene E-Mail-Adresse gesendet";

  @override
  String get lblLoginTitle => "Hallo wieder ";

  @override
  String get lblLoginSubTitle => "Willkommen zurück, Sie wurden lange vermisst";

  @override
  String get lblOrContinueWith => "Oder fortfahren";

  @override
  String get lblHelloUser => "Hallo Benutzer!";

  @override
  String get lblSignUpSubTitle => 'Erstellen Sie Ihr Konto für eine bessere Erfahrung';

  @override
  String get lblStepper1Title => "Geben Sie die Detailinformationen ein";

  @override
  String get lblDateAndTime => "Datum (und Uhrzeit:";

  @override
  String get chooseDateAndTime => 'Wählen Sie Datum und Uhrzeit';

  @override
  String get lblYourAddress => "Deine Adresse";

  @override
  String get lblEnterYourAddress => "Geben Sie Ihre Adresse ein";

  @override
  String get lblUseCurrentLocation => "Verwenden Sie den aktuellen Standort";

  @override
  String get lblEnterDescription => "Beschreibung eingeben";

  @override
  String get lblPrice => "Preis";

  @override
  String get lblTax => "Steuer";

  @override
  String get lblDiscount => "Rabatt";

  @override
  String get lblAvailableCoupons => "Verfügbare Gutscheine";

  @override
  String get lblPrevious => "Vorherige";

  @override
  String get lblCoupon => "Coupon";

  @override
  String get lblEditYourReview => "Bearbeiten Sie Ihre Bewertung.";

  @override
  String get lblTime => "Zeit";

  @override
  String get textProvider => "Anbieter";

  @override
  String get lblConfirmBooking => "Buchung bestätigen";

  @override
  String get lblConfirmMsg => "Möchten Sie diese Buchung sicher, dass Sie diese Buchung bestätigen möchten?";

  @override
  String get lblCancel => "Stornieren";

  @override
  String get lblExpiryDate => "Verfallsdatum :";

  @override
  String get lblRemoveCoupon => "Gutschein entfernen.";

  @override
  String get lblNoCouponsAvailable => "Keine Gutscheine verfügbar.";

  @override
  String get lblStep1 => "Schritt 1";

  @override
  String get lblStep2 => "Schritt 2";

  @override
  String get lblBookingID => "Buchungs-ID.";

  @override
  String get lblDate => "Datum";

  @override
  String get lblAboutHandyman => "Über Handwerker.";

  @override
  String get lblAboutProvider => "Über den Anbieter.";

  @override
  String get lblNotRatedYet => "Sie haben noch nicht bewertet";

  @override
  String get lblDeleteReview => "Überprüfung löschen";

  @override
  String get lblConfirmReviewSubTitle => 'Möchten Sie diese Bewertung löschen?';

  @override
  String get lblConfirmService => 'Möchten Sie diesen Gottesdienst abhalten?';

  @override
  String get lblConFirmResumeService => 'Möchten Sie diesen Dienst fortsetzen?';

  @override
  String get lblEndServicesMsg => "Möchten Sie diesen Service beenden?";

  @override
  String get lblCancelBooking => "Buchung stornieren";

  @override
  String get lblStart => "Start";

  @override
  String get lblHold => "Halten";

  @override
  String get lblResume => "Fortsetzen";

  @override
  String get lblPayNow => "Zahlen Sie jetzt";

  @override
  String get lblCheckStatus => "Status überprüfen";

  @override
  String get lblID => "ICH WÜRDE";

  @override
  String get lblNoBookingsFound => "Keine Buchungen gefunden";

  @override
  String get lblCategory => "Kategorie";

  @override
  String get lblYourComment => "Dein Kommentar";

  @override
  String get lblIntroducingCustomerRating => "Einführung der Kundenbewertung.";

  @override
  String get lblSeeYourRatings => "Sehen Sie Ihre Bewertungen an";

  @override
  String get lblFeatured => "Vorgestellt";

  @override
  String get lblNoServicesFound => "Keine Dienste gefunden";

  @override
  String get lblGENERAL => "ALLGEMEINES";

  @override
  String get lblAboutApp => "Über die App.";

  @override
  String get lblPurchaseCode => "Vollständiger Quellcode kaufen";

  @override
  String get lblNoRateYet => "Derzeit haben Sie keine Dienste bewertet";

  @override
  String get lblMemberSince => "Mitglied seit";

  @override
  String get lblFilterBy => "Filtern nach";

  @override
  String get lblClearFilter => "Filter klären";

  @override
  String get lblNoReviews => "Keine Bewertungen";

  @override
  String get lblUnreadNotification => "Ungelesene Benachrichtigung";

  @override
  String get lblChoosePaymentMethod => "Zahlungsart auswählen";

  @override
  String get lblNoPayments => "Keine Zahlungen";

  @override
  String get lblPayWith => "Möchten Sie mit zahlen?";

  @override
  String get payWith => "Bezahlen mit";

  @override
  String get lblYourRating => "Deine Bewertung";

  @override
  String get lblEnterReview => "Geben Sie Ihre Bewertung ein (optional)";

  @override
  String get lblDelete => "Löschen";

  @override
  String get lblDeleteRatingMsg => 'Möchten Sie diese Bewertung löschen?';

  @override
  String get lblSelectRating => 'Bewertung ist erforderlich';

  @override
  String get lblNoServiceRatings => "Keine Service-Ratings";

  @override
  String get lblSearchFor => "Suchen nach";

  @override
  String get lblRating => "Nennen";

  @override
  String get lblAvailableAt => "Verfügbar um";

  @override
  String get lblRelatedServices => "Zugehörige Dienstleistungen.";

  @override
  String get lblBookNow => "buchen Sie jetzt";

  @override
  String get lblWelcomeToHandyman => "Willkommen bei $APP_NAME.";

  @override
  String get lblWalkThroughSubTitle => "$APP_NAME Service - On-Demand-Home Services-App mit vollständiger Lösung";

  @override
  String get textHandyman => "Handyman";

  @override
  String get lblChooseFromMap => "Wähle aus der Karte";

  @override
  String get lblDeleteAddress => "Adresse löschen";

  @override
  String get lblDeleteSunTitle => 'Möchten Sie diese Adresse löschen?';

  @override
  String get lblFaq => "FAQs";

  @override
  String get lblServiceFaq => "Service-FAQs.";

  @override
  String get lblLogoutTitle => "Oh nein, du gehst!";

  @override
  String get lblLogoutSubTitle => "Möchten Sie sich abmelden?";

  @override
  String get lblFeaturedProduct => "Dies ist ein Produkt";

  @override
  String get lblAlert => "Alarm";

  @override
  String get lblOnBase => "Auf der Grundlage von";

  @override
  String get lblInvalidCoupon => "Gutscheincode ist ungültig";

  @override
  String get lblSelectCode => "Bitte wählen Sie den Gutscheincode aus";

  @override
  String get lblBackPressMsg => "Drücken Sie erneut, um die App zu verlassen";

  @override
  String get lblHour => "Stunde";

  @override
  String get lblHelplineNumber => "Helpline -Nummer";

  @override
  String get lblSubcategories => "Unterkategorien";

  @override
  String get lblAgree => "Ich stimme dem zu";

  @override
  String get lblTermsOfService => "Nutzungsbedingungen";

  @override
  String get lblWalkThrough0 => "Handwerkerservice - On -Demand Home Services App mit komplette Lösung";

  @override
  String get lblServiceTotalTime => "Service Gesamtzeit";

  @override
  String get lblDateTimeUpdated => 'Ihr Buchungsdatum und Ihre Uhrzeit wurden erfolgreich abgeschlossen';

  @override
  String get lblSelectDate => "Bitte wählen Sie Datum Uhrzeit";

  @override
  String get lblReasonCancelling => "Grund:";

  @override
  String get lblReasonRejecting => "Grund für die Ablehnung dieser Buchung";

  @override
  String get lblFailed => "Grund, warum diese Buchung gescheitert ist";

  @override
  String get lblNotDescription => "Keine Beschreibung verfügbar";

  @override
  String get lblMaterialTheme => "Aktivieren Sie das Material, das Sie Themen haben";

  @override
  String get lblServiceProof => "Service -Beweis";

  @override
  String get lblAndroid12Support => "Diese Aktion startet Ihre App neu. Bestätigen Sie?";

  @override
  String get lblOff => "Rabatt";

  @override
  String get lblHr => "Stunde";

  @override
  String get lblSignInWithGoogle => "Anmeldung mit Google";

  @override
  String get lblSignInWithOTP => "Melden Sie sich mit OTP an";

  @override
  String get lblDangerZone => "Gefahrenzone";

  @override
  String get lblDeleteAccount => "Konto löschen";

  @override
  String get lblUnderMaintenance => "Wird gewartet...";

  @override
  String get lblCatchUpAfterAWhile => "Nach einer Weile aufholen";

  @override
  String get lblId => "Ausweis";

  @override
  String get lblMethod => "Methode";

  @override
  String get lblStatus => "Status";

  @override
  String get lblPending => "Ausstehend";

  @override
  String get confirmationRequestTxt => 'Möchten Sie diese Aktion ausführen?';

  @override
  String get lblDeleteAccountConformation => "Ihr Konto wird dauerhaft gelöscht. Ihre Daten werden nicht wieder wiederhergestellt.";

  @override
  String get lblAutoSliderStatus => "Auto -Slider -Status";

  @override
  String get lblPickAddress => "Adresse auswählen";

  @override
  String get lblUpdateDateAndTime => "Datum und Uhrzeit aktualisieren";

  @override
  String get lblRecheck => "Überprüfung";

  @override
  String get lblLoginAgain => "Bitte melden Sie sich erneut an";

  @override
  String get lblUpdate => "Aktualisieren";

  @override
  String get lblNewUpdate => "Neues Update";

  @override
  String get lblOptionalUpdateNotify => "Optionales Update Benachrichtigung";

  @override
  String get lblAnUpdateTo => "Ein Update zu";

  @override
  String get lblIsAvailableWouldYouLike => "ist verfügbar. Möchten sie updaten?";

  @override
  String get lblRegisterAsPartner => "Registrieren Sie sich als Partner";

  @override
  String get lblSignInWithApple => "Melden Sie sich mit Apple an";

  @override
  String get lblWaitingForProviderApproval => "Warten auf die Genehmigung von Anbietern";

  @override
  String get lblFree => "unbezahlt";

  @override
  String get lblAppleSignInNotAvailable => "Apple Signin ist für Ihr Gerät nicht verfügbar";

  @override
  String get lblTotalExtraCharges => "Gesamt zusätzliche Gebühren";

  @override
  String get lblWaitingForResponse => "Auf Rückantwort warten";

  @override
  String get lblAll => "Alle";

  @override
  String get noConversation => "Kein Gespräch";

  @override
  String get noConversationSubTitle => "Sie haben noch kein Gespräch geführt. Bitte buchen Sie einen Dienst, um mit einem Anbieter zu chatten.";

  @override
  String get noBookingSubTitle => "Sieht so aus, als hätten Sie Ihre Bestellung noch nicht gebucht";

  @override
  String get myReviews => "Meine Bewertungen";

  @override
  String get noCategoryFound => "Keine Kategorie gefunden";

  @override
  String get noProviderFound => "Kein Anbieter gefunden";

  @override
  String get createServiceRequest => "Serviceanfrage erstellen";

  @override
  String get chooseImages => "Wählen Sie Bilder";

  @override
  String get serviceDescription => "Leistungsbeschreibung";

  @override
  String get addNewService => "Neuen Service hinzufügen";

  @override
  String get newPostJobRequest => 'Neue Jobanfrage stellen';

  @override
  String get postJobTitle => "Post -Berufsbezeichnung";

  @override
  String get postJobDescription => "Post -Job -Beschreibung";

  @override
  String get services => "Dienstleistungen";

  @override
  String get myPostJobList => "Meine individuelle Jobanfrage";

  @override
  String get requestNewJob => "Fordern Sie einen neuen Job an";

  @override
  String get noNotifications => "Keine Benachrichtigungen";

  @override
  String get noNotificationsSubTitle => "Wir werden Sie benachrichtigen, sobald wir etwas für Sie haben";

  @override
  String get noFavouriteSubTitle => "Ihre Lieblingsdienste werden hier erscheinen";

  @override
  String get termsConditionsAccept => "Bitte akzeptieren Sie Allgemeine Geschäftsbedingungen";

  @override
  String get disclaimer => "Haftungsausschluss";

  @override
  String get disclaimerContent => "Sie werden nach Abschluss Ihrer Buchung um Zahlung gefragt.";

  @override
  String get inputMustBeNumberOrDigit => 'Die Eingabe muss Anzahl oder Ziffer sein';

  @override
  String get requiredAfterCountryCode => 'nach Ländercode erforderlich';

  @override
  String get selectedOtherBookingTime => 'Die ausgewählte Buchungszeit ist bereits übergeben. Bitte wählen Sie ein anderes Mal.';

  @override
  String get myServices => 'Meine Dienstleistungen';

  @override
  String get doYouWantToAssign => 'Möchten Sie zuweisen?';

  @override
  String get bidPrice => 'Angebotspreis';

  @override
  String get accept => 'Annehmen';

  @override
  String get price => 'Preis';

  @override
  String get remove => 'Entfernen';

  @override
  String get add => 'Hinzufügen';

  @override
  String get save => 'Speichern';

  @override
  String get createPostJobWithoutSelectService => 'Sie können keinen Postjob erstellen, ohne den Service auszuwählen';

  @override
  String get selectCategory => 'Kategorie wählen';

  @override
  String get pleaseAddImage => 'Bitte fügen Sie Bild hinzu';

  @override
  String get selectedBookingTimeIsAlreadyPassed => 'Die ausgewählte Buchungszeit ist bereits übergeben. Bitte wählen Sie ein anderes Mal.';

  @override
  String get jobPrice => 'Arbeitspreis';

  @override
  String get estimatedPrice => 'Schätzpreis';

  @override
  String get bidder => 'Bieter';

  @override
  String get assignedProvider => 'Zugewiesener Anbieter';

  @override
  String get myPostDetail => 'Mein Postdetail';

  @override
  String get thankYou => 'Danke schön!';

  @override
  String get bookingConfirmedMsg => 'Ihre Buchung ist bestätigt.';

  @override
  String get goToHome => 'Zur Startseite';

  @override
  String get goToReview => 'Gehen Sie zur Überprüfung';

  @override
  String get noServiceAdded => 'Kein Service hinzugefügt';

  @override
  String get noPostJobFound => 'Kein Postjob gefunden';

  @override
  String get noPostJobFoundSubtitle => 'Wenn Sie Ihren Job veröffentlichen, wird jeder Anbieter benachrichtigt, und Sie können Ihren gewünschten Anbieter auswählen, um den Job zu erledigen.';

  @override
  String get pleaseEnterValidOTP => 'Bitte geben Sie gültige OTP ein';

  @override
  String get confirmOTP => 'Bestätigen Sie OTP';

  @override
  String get sendingOTP => 'Senden von OTP';

  @override
  String get pleaseSelectDifferentSlotThenPrevious => 'Bitte wählen Sie einen anderen Slot als vorherige Slot';

  @override
  String get pleaseSelectTheSlotsFirst => 'Bitte wählen Sie zuerst die Slots aus';

  @override
  String get editTimeSlotsBooking => 'Zeitfenster bearbeiten Buchung';

  @override
  String get availableSlots => 'Verfügbare Plätze';

  @override
  String get noTimeSlots => 'Keine Zeitfenster';

  @override
  String get bookingDateAndSlot => 'Ooking Date & Slot';

  @override
  String get extraCharges => 'Extrakosten';

  @override
  String get chatCleared => 'Chat gelöscht';

  @override
  String get clearChat => 'Chat löschen';

  @override
  String get jobRequestSubtitle => 'Finden Sie Ihren Service nicht? Machen Sie sich keine Sorgen, Sie können Ihre Anforderungen veröffentlichen.';

  @override
  String get verified => 'Verifiziert';

  @override
  String get theEnteredCodeIsInvalidPleaseTryAgain => 'Der eingegebene Code ist ungültig. Bitte versuchen Sie es erneut';

  @override
  String get otpCodeIsSentToYourMobileNumber => 'OTP -Code wird an Ihre Handynummer gesendet';

  @override
  String get yourPaymentFailedPleaseTryAgain => 'Ihre Zahlung ist fehlgeschlagen. Bitte versuchen Sie es erneut';

  @override
  String get yourPaymentHasBeenMadeSuccessfully => 'Ihre Zahlung wurde erfolgreich geleistet';

  @override
  String get transactionFailed => 'Transaktion fehlgeschlagen';

  @override
  String get lblStep3 => 'Schritt 3';

  @override
  String get lblAvailableOnTheseDays => 'An diesen Tagen erhältlich';

  @override
  String get internetNotAvailable => 'Ihr Internet scheint offline zu sein';

  @override
  String get pleaseTryAgain => 'Bitte versuche es erneut';

  @override
  String get somethingWentWrong => 'Etwas ist schief gelaufen';

  @override
  String get postJob => 'Postjob';

  @override
  String get package => 'Paket';

  @override
  String get frequentlyBoughtTogether => 'Wird oft zusammen gekauft';

  @override
  String get endOn => 'Endet auf';

  @override
  String get buy => 'Kaufen';

  @override
  String get includedServices => 'Eingeschlossene Dienstleistungen';

  @override
  String get includedInThisPackage => 'In diesem Paket enthalten';

  @override
  String get lblInvalidTransaction => 'Ungültige Transaktion';

  @override
  String get getTheseServiceWithThisPackage => 'Sie erhalten diese Dienste mit diesem Paket';

  @override
  String get lblNotValidUser => 'Sie sind kein gültiger Benutzer';

  @override
  String get lblSkip => 'Überspringen';

  @override
  String get lblChangeCountry => 'Land ändern';

  @override
  String get lblTimeSlotNotAvailable => 'Dieser Slot ist nicht verfügbar';

  @override
  String get lblAdd => 'hinzufügen';

  @override
  String get lblThisService => 'Der Service';

  @override
  String get lblYourCurrenciesNotSupport => 'Ihre Währungen unterstützen Cinetpay nicht';

  @override
  String get lblSignInFailed => 'Anmeldung fehlgeschlagen';

  @override
  String get lblUserCancelled => 'Benutzer abgesagt';

  @override
  String get lblTransactionCancelled => 'Transaktion abgesagt';

  @override
  String get lblExample => 'Beispiel';

  @override
  String get lblCheckOutWithCinetPay => 'Kasse mit Cinetpay';

  @override
  String get lblLocationPermissionDenied => 'Standortberechtigungen werden abgelehnt.';

  @override
  String get lblLocationPermissionDeniedPermanently => 'Standortberechtigungen werden dauerhaft abgelehnt, wir können keine Berechtigungen beantragen.';

  @override
  String get lblEnableLocation => 'Bitte stellen Sie sicher, dass Standortdienste aktiviert sind.';

  @override
  String get lblNoUserFound => 'Kein Benutzer gefunden';

  @override
  String get lblUserNotCreated => 'Benutzer nicht erstellt';

  @override
  String get lblTokenExpired => 'Token lief ab';

  @override
  String get lblConfirmationForDeleteMsg => 'Möchten Sie die Nachricht löschen?';

  @override
  String get favouriteProvider => 'Lieblingsanbieter';

  @override
  String get noProviderFoundMessage => 'Ihre Lieblingsanbieter werden hier erscheinen';

  @override
  String get personalInfo => 'Persönliche Informationen';

  @override
  String get essentialSkills => 'Essenzielle Fähigkeiten';

  @override
  String get knownLanguages => 'Bekannte Sprachen';

  @override
  String get authorBy => 'Autor von';

  @override
  String get views => 'Ansichten';

  @override
  String get blogs => 'Blogs';

  @override
  String get noBlogsFound => 'Keine Blogs gefunden';

  @override
  String get requestInvoice => 'Rechnung anfordern';

  @override
  String get invoiceSubTitle => 'Geben Sie die E -Mail -Adresse ein, an der Sie Ihre Rechnung erhalten möchten';

  @override
  String get sentInvoiceText => 'Bitte überprüfen Sie Ihre E -Mails, die wir in Ihrer E -Mail -Rechnungen gesendet haben.';

  @override
  String get send => 'Schicken';

  @override
  String get published => 'Veröffentlicht';

  @override
  String get clearChatMessage => 'Möchten Sie diesen Chat löschen?';

  @override
  String get deleteMessage => 'Möchten Sie löschen?';

  @override
  String get accepted => 'Akzeptiert';

  @override
  String get onGoing => 'Beim Gehen';

  @override
  String get inProgress => 'Im Gange';

  @override
  String get cancelled => 'Abgesagt';

  @override
  String get rejected => 'Abgelehnt';

  @override
  String get failed => 'Fehlgeschlagen';

  @override
  String get completed => 'Vollendet';

  @override
  String get pendingApproval => 'Ausstehende Genehmigung';

  @override
  String get waiting => 'Warten';

  @override
  String get paid => 'Bezahlt';

  @override
  String get advancePaid => 'Vorab bezahlt';

  @override
  String get insufficientBalanceMessage => 'Sie haben eine unzureichende Balance in Ihrer Brieftasche. Bitte wählen Sie eine andere Methode.';

  @override
  String get cinetPayNotSupportedMessage => 'Cinetpay wird nicht von Ihren Währungen unterstützt';

  @override
  String get loading => 'Wird geladen..';

  @override
  String get walletBalance => 'Brieftaschenbalance';

  @override
  String get payAdvance => 'Voraus bezahlen';

  @override
  String get advancePaymentMessage => 'Nehmen Sie eine Vorauszahlung vor, um eine Buchung abzuschließen';

  @override
  String get advancePayAmount => 'Vorauszahlungsbetrag';

  @override
  String get remainingAmount => 'Restbetrag';

  @override
  String get advancePayment => 'Vorauszahlung';

  @override
  String get withExtraAndAdvanceCharge => 'Mit zusätzlicher Gebühr und Vorauszahlung';

  @override
  String get withExtraCharge => 'Mit zusätzlicher Gebühr';

  @override
  String get min => 'Mindest';

  @override
  String get hour => 'Stunde';

  @override
  String get customerRatingMessage => 'Sagen Sie anderen, was Sie denken';

  @override
  String get paymentHistory => 'Zahlungshistorie';

  @override
  String get message => 'Nachricht';

  @override
  String get wallet => 'Geldbörse';

  @override
  String get payWithFlutterWave => 'Mit Flutterwave bezahlen';

  @override
  String get goodMorning => 'Guten Morgen';

  @override
  String get goodAfternoon => 'Guten Tag';

  @override
  String get goodEvening => 'Guten Abend';

  @override
  String get invalidURL => 'ungültige URL';

  @override
  String get use24HourFormat => '24-Stunden-Format verwenden?';

  @override
  String get email => 'Email';

  @override
  String get badRequest => 'Schlechte Anfrage';

  @override
  String get forbidden => 'Verboten';

  @override
  String get pageNotFound => 'Seite nicht gefunden';

  @override
  String get tooManyRequests => 'Zu viele Anfragen';

  @override
  String get internalServerError => 'Interner Serverfehler';

  @override
  String get badGateway => 'Bad Gateway';

  @override
  String get serviceUnavailable => 'Dienst nicht verfügbar';

  @override
  String get gatewayTimeout => 'Gateway Timeout';

  @override
  String get pleaseWait => 'Warten Sie mal';

  @override
  String get externalWallet => 'Außenbrieftasche';

  @override
  String get userNotFound => 'Benutzer nicht gefunden';

  @override
  String get requested => 'Angefordert';

  @override
  String get assigned => 'Zugewiesen';

  @override
  String get reload => 'Neu laden';

  @override
  String get lblStripeTestCredential => 'Testen von Anmeldeinformationen können nicht mehr als 500 bezahlen';

  @override
  String get noDataFoundInFilter => 'Wählen Sie die besten Filterkriterien, um die besten Ergebnisse zu erzielen';

  @override
  String get addYourCountryCode => 'Fügen Sie Ihren Ländercode hinzu';

  @override
  String get help => 'Hilfe';

  @override
  String get couponCantApplied => 'Dieser Gutschein kann nicht angewendet werden';

  @override
  String get priceAmountValidationMessage => 'Der Preisbetrag sollte verstreut sein als 0';

  @override
  String get pleaseWaitWhileWeLoadChatDetails => 'Bitte warten Sie, während wir Chat -Details laden';

  @override
  String get isNotAvailableForChat => 'ist nicht für den Chat verfügbar';

  @override
  String get connectWithFirebaseForChat => 'Connectez-vous avec Firebase pour le chat';

  @override
  String get closeApp => 'Close App';

  @override
  String get providerAddedToFavourite => 'Anbieter zur Lieblingsliste hinzugefügt';

  @override
  String get providerRemovedFromFavourite => 'Anbieter von der Lieblingsliste entfernt';

  @override
  String get provideValidCurrentPasswordMessage => 'Sie müssen ein gültiges aktuelles Passwort angeben';

  @override
  String get copied => 'Kopiert';

  @override
  String get copyMessage => 'Nachricht kopieren';

  @override
  String get messageDelete => 'Nachricht löschen';

  @override
  String get pleaseChooseAnyOnePayment => 'Bitte wählen Sie eine Zahlungsmethode aus';

  @override
  String get myWallet => 'Mein Geldbeutel';

  @override
  String get balance => 'Gleichgewicht';

  @override
  String get topUpWallet => 'Top-up-Brieftasche';

  @override
  String get topUpAmountQuestion => 'Mit welcher Menge würden Sie lieber auffüllen?';

  @override
  String get paymentMethod => 'Bezahlverfahren';

  @override
  String get selectYourPaymentMethodToAddBalance => 'Wählen Sie Ihre Zahlungsmethode aus, um den Guthaben hinzuzufügen';

  @override
  String get proceedToTopUp => 'Fahren Sie mit der Aufladung fort';

  @override
  String get serviceAddedToFavourite => 'Service zur Lieblingsliste hinzugefügt';

  @override
  String get serviceRemovedFromFavourite => 'Service aus der Lieblingsliste entfernt';

  @override
  String get firebaseRemoteCannotBe => 'Firebase -Fernbedienung kann nicht verbunden werden';

  @override
  String get search => 'Suchen';

  @override
  String get close => 'Schließen';

  @override
  String get totalAmountShouldBeMoreThan => 'Gesamtbetrag sollte mehr als sein';

  @override
  String get totalAmountShouldBeLessThan => 'Die Gesamtmenge sollte geringer sein als';

  @override
  String get doYouWantToTopUpYourWallet => 'Möchten Sie jetzt Ihre Brieftasche auffüllen?';

  @override
  String get chooseYourLocation => 'Wähle deinen Standort';

  @override
  String get connect => 'Verbinden';

  @override
  String get transactionId => 'Transaktions-ID';

  @override
  String get at => 'bei';

  @override
  String get appliedTaxes => 'Angewandte Steuern';

  @override
  String get accessDeniedContactYourAdmin => 'Zugriff abgelehnt. Wenden Sie sich an Ihren Administrator, um Unterstützung zu erhalten.';

  @override
  String get yourWalletIsUpdated => 'Ihre Brieftasche ist aktualisiert!';

  @override
  String get by => 'von';

  @override
  String get noPaymentMethodFound => 'Keine Zahlungsmethode gefunden';

  @override
  String get theAmountShouldBeEntered => 'Der Betrag sollte eingegeben werden';

  @override
  String get walletHistory => 'Brieftaschengeschichte';

  @override
  String get debit => 'Lastschrift';

  @override
  String get credit => 'Kredit';

  @override
  String get youCannotApplyThisCoupon => 'Sie können diesen Gutschein nicht anwenden';

  @override
  String get basedOn => 'Bezogen auf';

  @override
  String get serviceStatusPicMessage => 'Bitte stellen Sie sicher, dass Sie mindestens einen Buchungsstatus auswählen';

  @override
  String get clearFilter => 'Klaren Filter';

  @override
  String get bookingStatus => 'Buchungsstatus';

  @override
  String get addOns => 'Add-Ons';

  @override
  String get serviceAddOns => 'Service-Add-Ons';

  @override
  String get turnOn => 'Anmachen';

  @override
  String get turnOff => 'Abschalten';

  @override
  String get serviceVisitType => 'Type de visite de service';

  @override
  String get thisServiceIsOnlineRemote => 'Dieser Service wird online/remote abgeschlossen sein.';

  @override
  String get deleteMessageForAddOnService => 'Voulez-vous supprimer ce service complémentaire?';

  @override
  String get confirmation => 'Bestätigung!';

  @override
  String get pleaseNoteThatAllServiceMarkedCompleted => 'Bitte beachten Sie, dass alle Service-Add-Ons als abgeschlossen markiert sind!';

  @override
  String get writeHere => 'Hier schreiben';

  @override
  String get isAvailableGoTo => 'ist verfügbar. Gehen Sie zum Store ab und laden Sie die neue Version der App herunter.';

  @override
  String get later => 'Später';

  @override
  String get whyChooseMe => 'Warum mich wählen?';

  @override
  String get useThisCodeToGet => 'Verwenden Sie diesen Code, um zu erhalten';

  @override
  String get off => 'aus';

  @override
  String get applied => 'Angewandt';

  @override
  String get coupons => 'Gutscheine';

  @override
  String get handymanList => 'Handwerkerliste';

  @override
  String get noHandymanFound => 'Kein Handwerker gefunden';

  @override
  String get back => 'Zurück';

  @override
  String get team => 'Team';

  @override
  String get whyChooseMeAs => 'Warum wählen Sie mich als Ihren vertrauenswürdigen Dienstleister?';

  @override
  String get reason => 'Grund';

  @override
  String get pleaseEnterAddressAnd => 'Bitte geben Sie Adresse und Buchungsdatum und Slot ein';

  @override
  String get pleaseEnterYourAddress => 'Bitte geben Sie Ihre Adresse ein';

  @override
  String get pleaseSelectBookingDate => 'Bitte wählen Sie Buchungsdatum und Slot';

  @override
  String get doYouWantTo => 'Möchten Sie diesen Gutschein entfernen?';

  @override
  String get chooseDateTime => 'Wählen Sie Datum und Uhrzeit';

  @override
  String get airtelMoneyPayment => 'Airtel -Geldzahlung';

  @override
  String get recommendedForYou => 'für dich empfohlen';

  @override
  String get paymentSuccess => 'Zahlungserfolg';

  @override
  String get redirectingToBookings => 'Umleitung zu Buchungen ..';

  @override
  String get transactionIsInProcess => 'Transaktion ist im Prozess ...';

  @override
  String get pleaseCheckThePayment => 'Bitte überprüfen Sie, ob die Zahlungsanfrage an Ihre Nummer gesendet wird';

  @override
  String get enterYourMsisdnHere => 'Geben Sie hier Ihre MSISDN ein';

  @override
  String get theTransactionIsStill => 'Die Transaktion verarbeitet immer noch und befindet sich im zweideutigen Zustand. Bitte führen Sie die Transaktionsanfrage durch, um den Transaktionsstatus abzurufen.';

  @override
  String get transactionIsSuccessful => 'Transaktion ist erfolgreich';

  @override
  String get incorrectPinHasBeen => 'Eine falsche PIN wurde eingegeben';

  @override
  String get theUserHasExceeded => 'Der Benutzer hat seine von der Brieftasche zugelassene Transaktionsgrenze überschritten';

  @override
  String get theAmountUserIs => 'Der Betrag, den der Benutzer überträgt, ist geringer als der zulässige Mindestbetrag';

  @override
  String get userDidnTEnterThePin => 'Der Benutzer hat die PIN nicht eingegeben';

  @override
  String get transactionInPendingState => 'Transaktion im ausstehenden Zustand. Bitte überprüfen Sie nach einiger Zeit';

  @override
  String get userWalletDoesNot => 'Benutzerbrieftasche hat nicht genug Geld, um den zahlenden Betrag abzudecken';

  @override
  String get theTransactionWasRefused => 'Die Transaktion wurde abgelehnt';

  @override
  String get thisIsAGeneric => 'Dies ist eine generische Ablehnung, die mehrere mögliche Ursachen hat';

  @override
  String get payeeIsAlreadyInitiated => 'Der Zahlungsempfänger ist bereits für die Abwanderung oder für die Barred oder nicht auf der Airtel Money -Plattform eingeleitet';

  @override
  String get theTransactionWasTimed => 'Die Transaktion wurde zeitlich festgelegt.';

  @override
  String get theTransactionWasNot => 'Die Transaktion wurde nicht gefunden.';

  @override
  String get xSignatureAndPayloadDid => 'X-Signatur und Nutzlast stimmten nicht überein';

  @override
  String get encryptionKeyHasBeen => 'Der Verschlüsselungsschlüssel wurde erfolgreich abgerufen';

  @override
  String get couldNotFetchEncryption => 'Konnte den Verschlüsselungsschlüssel nicht holen';

  @override
  String get transactionHasBeenExpired => 'Die Transaktion wurde abgelaufen';

  @override
  String get ambiguous => 'Mehrdeutig';

  @override
  String get success => 'Erfolg';

  @override
  String get incorrectPin => 'Falsche Pin';

  @override
  String get exceedsWithdrawalAmountLimitS => 'Überschreitet die Auszahlungsbetragsgrenze (en) / Abhebungsbetragsgrenze überschritten';

  @override
  String get invalidAmount => 'Ungültige Menge';

  @override
  String get transactionIdIsInvalid => 'Die Transaktions -ID ist ungültig';

  @override
  String get inProcess => 'In Bearbeitung';

  @override
  String get notEnoughBalance => 'Nicht genug Gleichgewicht';

  @override
  String get refused => 'Abgelehnt';

  @override
  String get doNotHonor => 'Nicht ehren';

  @override
  String get transactionNotPermittedTo => 'Transaktion nicht dem Zahlungsempfänger gestattet';

  @override
  String get transactionTimedOut => 'Transaktionszeitpunkt';

  @override
  String get transactionNotFound => 'Transaktion nicht gefunden';

  @override
  String get forBidden => 'Verboten';

  @override
  String get successfullyFetchedEncryptionKey => 'Erfolgreich abgerufene Verschlüsselungsschlüssel';

  @override
  String get errorWhileFetchingEncryption => 'Fehler beim Abrufen von Verschlüsselungsschlüssel';

  @override
  String get transactionExpired => 'Transaktion abgelaufen';

  @override
  String get verifyEmail => 'E-Mail bestätigen';

  @override
  String get minRead => 'min lesen';

  @override
  String get loadingChats => 'Chats laden ...';

  @override
  String get monthly => 'Monatlich';

  @override
  String get noCouponsAvailableMsg => 'Im Moment keine Gutscheine. Schauen Sie weiter nach exklusiven Angeboten zurück!';

  @override
  String get refundPolicy => 'Rückgaberecht';

  @override
  String get chooseAnyOnePayment => 'Wählen Sie zuerst eine Zahlungsmethode aus';

  @override
  String get january => 'Januar';

  @override
  String get february => 'Februar';

  @override
  String get march => 'Marsch';

  @override
  String get april => 'April';

  @override
  String get may => 'Mai';

  @override
  String get june => 'Juni';

  @override
  String get july => 'Juli';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'Oktober';

  @override
  String get november => 'November';

  @override
  String get december => 'Dezember';

  @override
  String get monthName => 'Monatsname';

  @override
  String get mon => 'Mo';

  @override
  String get tue => 'Di';

  @override
  String get wed => 'Heiraten';

  @override
  String get thu => 'Do';

  @override
  String get fri => 'Fr';

  @override
  String get sat => 'Sa';

  @override
  String get sun => 'Sonne';

  @override
  String get weekName => 'Wochenname';

  @override
  String get removeThisFile => 'Entfernen Sie diese Datei';

  @override
  String get areYouSureWantToRemoveThisFile => 'Möchten Sie diese Datei entfernen?';

  @override
  String get sendMessage => 'Nachricht senden';

  @override
  String get youAreNotConnectedWithChatServer => 'Stellen Sie eine Verbindung zum Chat-Server her';

  @override
  String get NotConnectedWithChatServerMessage => 'Sie sind nicht mit dem Chatserver verbunden. Tippen Sie auf die Schaltfläche unten, um eine Verbindung herzustellen und mit dem Chatten zu beginnen';

  @override
  String get sentYouAMessage => 'Schickte dir eine Nachricht';

  @override
  String get pushNotification => 'Push-Benachrichtigung';

  @override
  String get yourBooking => 'Ihre Buchung';

  @override
  String get featuredServices => 'Besondere Dienstleistungen';

  @override
  String get postYourRequestAnd => 'Veröffentlichen Sie Ihre Anfrage und wir werden unser Bestes tun, um sie zu erfüllen';

  @override
  String get newRequest => 'Neue Anfrage';

  @override
  String get upcomingBooking => 'Bevorstehende Buchung';

  @override
  String get theUserHasDenied => 'Der Benutzer hat die Verwendung der Spracherkennung abgelehnt';

  @override
  String get helloGuest => 'Hallo Gast';

  @override
  String get eGCleaningPlumberPest => 'z.B. Reinigung, Klempner, Schädlingsbekämpfung';

  @override
  String get ifYouDidnTFind => 'Wenn Sie unseren Service nicht gefunden haben, machen Sie sich keine Sorgen! Sie können Ihre Anfrage ganz einfach posten.';

  @override
  String get popularServices => 'Beliebte Dienste';

  @override
  String get canTFindYourServices => 'Sie können Ihre Dienste nicht finden?';

  @override
  String get trackProviderLocation => 'Verfolgen Sie den Standort des Anbieters';

  @override
  String get trackHandymanLocation => 'Verfolgen Sie den Standort des Handwerkers';

  @override
  String get handymanLocation => 'Handwerkerstandort';

  @override
  String get providerLocation => 'Standort des Anbieters';

  @override
  String get lastUpdatedAt => 'Zuletzt aktualisiert unter:';

  @override
  String get track => 'Schiene';

  @override
  String get handymanReached => 'Handwerker erreicht? Klicken Sie zum Starten';

  @override
  String get providerReached => 'Anbieter erreicht? Klicken Sie zum Starten';

  @override
  String get lblBankDetails => "Bankdaten";

  @override
  String get addBank => "Bank hinzufügen";

  @override
  String get bankList => "Bankliste";

  @override
  String get lbldefault => "Standard";

  @override
  String get setAsDefault => "Als Standard einstellen";

  @override
  String get aadharNumber => "Aadhar-Nummer";

  @override
  String get panNumber => "PAN-Nummer";

  @override
  String get lblPleaseEnterAccountNumber => "Bitte geben Sie die Kontonummer ein";

  @override
  String get lblAccountNumberMustContainOnlyDigits => "Die Kontonummer darf nur Ziffern enthalten";

  @override
  String get lblAccountNumberMustBetween11And16Digits => "Die Kontonummer muss zwischen 11 und 16 Ziffern lang sein";

  @override
  String get noBankDataTitle => "Keine Bankdaten gefunden";

  @override
  String get noBankDataSubTitle => "Sie haben noch keine Bank hinzugefügt";

  @override
  String get active => 'Aktiv';

  @override
  String get inactive => 'Inaktiv';

  @override
  String get deleteBankTitle => 'Möchten Sie diese Bank löschen?';

  @override
  String get lblEdit => 'Bearbeiten';

  @override
  String get bankName => 'Bank Name';

  @override
  String get accountNumber => 'Accountnummer';

  @override
  String get iFSCCode => 'IFSC -Code';

  @override
  String get availableBalance => "Verfügbares Guthaben";

  @override
  String get withdraw => "Zurückziehen";

  @override
  String get successful => 'Erfolgreich';

  @override
  String get yourWithdrawalRequestHasBeenSuccessfullySubmitted => 'Ihr Auszahlungsantrag wurde erfolgreich übermittelt.';

  @override
  String get eg3000 => 'zB" 3000"';

  @override
  String get chooseBank => "Bank auswählen";

  @override
  String get egCentralNationalBank => 'zB" Zentralbank"';

  @override
  String get topUp => "Aufladen";

  @override
  String get pleaseAddLessThanOrEqualTo => "Bitte addieren Sie kleiner oder gleich";

  @override
  String get withdrawRequest => "Antrag zurückziehen";

  @override
  String get lblEnterAmount => "Menge eingeben";

  @override
  String get btnSave => 'Speichern';

  @override
  String get fullNameOnBankAccount => 'Vollständiger Name auf dem Bankkonto';

  @override
  String get packageIsExpired => 'Paket ist abgelaufen';

  @override
  String get bookPackage => 'Buchpaket';

  @override
  String get packageDescription => 'Paketbeschreibung';

  @override
  String get packagePrice => 'Paketpreis';

  @override
  String get online => 'Online';

  @override
  String get noteAddressIsNot => 'Hinweis: Für Remote-Dienste ist keine Adresse erforderlich.';

  @override
  String get wouldYouLikeTo => 'Möchten Sie fortfahren und diese Buchung bestätigen?';

  @override
  String get packageName => 'Paketname';

  @override
  String get feeAppliesForCancellations => 'Für Stornierungen, die innerhalb dieser Frist erfolgen, fällt eine Gebühr an';

  @override
  String get a => 'A';

  @override
  String get byConfirmingYouAgree => 'Mit der Bestätigung erklären Sie sich mit unseren einverstanden';

  @override
  String get and => 'Und';

  @override
  String get areYouSureYou => 'Sind Sie sicher, dass Sie stornieren möchten? Je nach Servicepreis kann eine Stornogebühr anfallen';

  @override
  String get totalCancellationFee => 'Gesamte Stornogebühr';

  @override
  String get goBack => 'Geh zurück';

  @override
  String get bookingCancelled => 'Buchung storniert';

  @override
  String get yourBookingHasBeen => 'Ihre Buchung wurde erfolgreich storniert. Die entsprechende Rückerstattung wird innerhalb von 24 Stunden bearbeitet';

  @override
  String get noteCheckYourBooking => 'Hinweis: Überprüfen Sie Ihren Buchungsverlauf auf Rückerstattungsdetails';

  @override
  String get cancelledReason => 'Grund für den Abbruch';

  @override
  String get refundPaymentDetails => 'Zahlungsdetails für die Rückerstattung';

  @override
  String get refundOf => 'Rückerstattung von';

  @override
  String get refundAmount => 'Rückerstattungsbetrag';

  @override
  String get cancellationFee => 'Stornogebühr';

  @override
  String get advancedPayment => 'Vorauszahlung';

  @override
  String get hoursOfTheScheduled => 'Stunden des geplanten Gottesdienstes';

  @override
  String get open => 'OFFEN';

  @override
  String get closed => 'GESCHLOSSEN';

  @override
  String get createBy => 'Erstellen von';

  @override
  String get repliedBy => 'Beantwortet von';

  @override
  String get closedBy => 'Geschlossen von';

  @override
  String get helpDesk => 'Helpdesk';

  @override
  String get addNew => 'Neu hinzufügen';

  @override
  String get queryYet => 'Noch eine Anfrage';

  @override
  String get toSubmitYourProblems => 'Um Ihre Probleme einzureichen, klicken Sie einfach auf die Schaltfläche „Hinzufügen“ und erläutern Sie Ihr Anliegen';

  @override
  String get noRecordsFoundFor => 'Keine Datensätze gefunden für';

  @override
  String get queries => 'Abfragen.';

  @override
  String get noActivityYet => 'Noch keine Aktivität';

  @override
  String get noRecordsFound => 'Keine Datensätze gefunden';

  @override
  String get reply => 'Antwort';

  @override
  String get eGDuringTheService => 'z.B. Während des Gottesdienstes wurden die Möbel versehentlich beschädigt.';

  @override
  String get doYouWantClosedThisQuery => 'Möchten Sie diese Abfrage schließen?';

  @override
  String get markAsClosed => 'Als geschlossen markieren';

  @override
  String get youCanMarkThis => 'Sie können dies als geschlossen markieren, wenn Sie mit unserer Antwort zufrieden sind';

  @override
  String get subject => 'Thema';

  @override
  String get eGDamagedFurniture => 'z.B. Beschädigte Möbel';

  @override
  String get closedOn => 'Geschlossen am:';

  @override
  String get on => 'An';

  @override
  String get showMessage => 'Nachricht anzeigen';

  @override
  String get yesterday => 'Gestern';

  @override
  String get chooseAction => 'Wählen Sie Aktion';

  @override
  String get chooseImage => 'Wählen Sie Bild';

  @override
  String get noteYouCanUpload => 'Hinweis: Sie können Bilder mit den Erweiterungen \'jpg\', \'png\', \'jpeg\' hochladen und nur ein Bild auswählen';

  @override
  String get removeImage => 'Bild entfernen';

  @override
  String get advancedRefund => 'Erweiterte Rückerstattung';

  @override
  String get lblService => 'Service';

  @override
  String get dateRange => 'Datumsbereich';

  @override
  String get paymentType => 'Zahlungsart';

  @override
  String get reset => 'Zurücksetzen';

  @override
  String get noStatusFound => 'Kein Status gefunden';

  @override
  String get selectStartDateEndDate => 'Wählen Sie Startdatum und Enddatum';

  @override
  String get handymanNotFound => 'Handwerker nicht gefunden';

  @override
  String get providerNotFound => 'Anbieter nicht gefunden';

  @override
  String get rateYourExperience => 'Bewerten Sie Ihre Erfahrung';

  @override
  String get weValueYourFeedback => 'Wir schätzen Ihr Feedback! Bitte bewerten Sie Ihre jüngsten Erfahrungen mit unserem Service';

  @override
  String get viewStatus => 'Status anzeigen';

  @override
  String get paymentInfo => 'Zahlungsinformationen';

  @override
  String get mobile => 'Mobile:';

  @override
  String get to => 'Zu';

  @override
  String get chooseYourDateRange => 'Wählen Sie Ihren Datumsbereich';

  @override
  String get asHandyman => 'Als Handwerker';

  @override
  String get passwordLengthShouldBe => 'Die Länge des Passworts sollte 8 bis 12 Zeichen betragen.';

  @override
  String get cash => 'Kasse';

  @override
  String get bank => 'Bank';

  @override
  String get razorPay => "RazorPay";

  @override
  String get payPal => "PayPal";

  @override
  String get stripe => "Stripe";

  @override
  String get payStack => "PayStack";

  @override
  String get flutterWave => "FlutterWave";

  @override
  String get paytm => "Paytm";

  @override
  String get airtelMoney => "Airtel Geld";

  @override
  String get cinet => "Cinet";

  @override
  String get midtrans => "Midtrans";

  @override
  String get sadadPayment => "Sadad";

  @override
  String get phonePe => "PhonePe";

  @override
  String get inAppPurchase => "In-App-Kauf";

  @override
  String get pix => "Pix";

  @override
  String get chooseWithdrawalMethod => "Wählen Sie die Auszahlungsmethode";
}