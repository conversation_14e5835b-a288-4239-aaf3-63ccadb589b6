import 'package:booking_system_flutter/locale/languages.dart';
import 'package:booking_system_flutter/utils/configs.dart';

class LanguageEn extends BaseLanguage {
  @override
  String get walkTitle1 => 'Create and Set Up Your Account';

  @override
  String get walkTitle2 => 'Browse and Book Services';

  @override
  String get walkTitle3 => 'Track and Manage Your Bookings';

  @override
  String get getStarted => 'Get Started';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get hintFirstNameTxt => 'First Name';

  @override
  String get hintLastNameTxt => 'Last Name';

  @override
  String get hintContactNumberTxt => 'Contact Number';

  @override
  String get hintEmailAddressTxt => 'Enter your email address';

  @override
  String get hintUserNameTxt => 'User Name';

  @override
  String get hintPasswordTxt => 'Password';

  @override
  String get hintReenterPasswordTxt => 'Re-enter Password';

  @override
  String get confirm => 'Confirm';

  @override
  String get hintEmailTxt => 'Email Address';

  @override
  String get forgotPassword => 'Forgot password?';

  @override
  String get alreadyHaveAccountTxt => 'Already have an Account?';

  @override
  String get rememberMe => 'Remember Me';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get camera => 'Camera';

  @override
  String get language => 'App Language';

  @override
  String get appTheme => 'App Theme';

  @override
  String get bookingHistory => 'Booking History';

  @override
  String get rateUs => 'Rate Us';

  @override
  String get termsCondition => 'Terms & Conditions';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get about => 'About';

  @override
  String get logout => 'Logout';

  @override
  String get chooseTheme => 'Choose the App Theme';

  @override
  String get selectCountry => 'Select Country';

  @override
  String get selectState => 'Select State';

  @override
  String get selectCity => 'Select City';

  @override
  String get changePassword => 'Change Password';

  @override
  String get passwordNotMatch => "Password does not match";

  @override
  String get doNotHaveAccount => "Don't have an account?";

  @override
  String get hintNewPasswordTxt => "New Password";

  @override
  String get hintOldPasswordTxt => "Old Password";

  @override
  String get hintAddress => 'Address';

  @override
  String get lblGallery => "Gallery";

  @override
  String get yourReview => "Your Review";

  @override
  String get review => "Reviews";

  @override
  String get hintDescription => "Description";

  @override
  String get lblApply => "Apply";

  @override
  String get bookTheService => "Book Service";

  @override
  String get contactAdmin => "Please contact with Admin";

  @override
  String get allServices => 'All Services';

  @override
  String get duration => 'Duration';

  @override
  String get hourly => "hourly";

  @override
  String get payment => "Payment";

  @override
  String get done => "Done";

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get applyCoupon => 'Apply Coupon';

  @override
  String get priceDetail => 'Price Detail';

  @override
  String get home => 'Home';

  @override
  String get category => 'Categories';

  @override
  String get booking => 'Bookings';

  @override
  String get profile => 'Profile';

  @override
  String get lblAlertBooking => 'Do you want to book the service?';

  @override
  String get serviceName => 'Service Name';

  @override
  String get service => 'Services';

  @override
  String get lblCancelReason => 'Please give reason for canceling this Booking';

  @override
  String get enterReason => "Specify your reason here";

  @override
  String get noDataAvailable => 'No Data Available';

  @override
  String get lblOk => 'Ok';

  @override
  String get paymentDetail => 'Payment Detail';

  @override
  String get paymentStatus => 'Payment Status';

  @override
  String get viewDetail => 'View Detail';

  @override
  String get appThemeLight => 'Light';

  @override
  String get appThemeDark => 'Dark';

  @override
  String get appThemeDefault => 'System default';

  @override
  String get markAsRead => "Mark all as Read";

  @override
  String get lblYes => 'Yes';

  @override
  String get lblNo => 'No';

  @override
  String get btnRate => "Rate Now ";

  @override
  String get btnSubmit => "Submit";

  @override
  String get walkThrough1 => 'Sign up or log in to your account using your email or social media profiles. Completing your profile ensures a seamless booking experience.';

  @override
  String get walkThrough2 =>
      'Explore a wide range of services available in your area. Select a service, choose a convenient time slot, and provide your location details to book a service quickly and easily.';

  @override
  String get walkThrough3 => 'Keep track of your service status in real-time. View and manage your current and past bookings. Reschedule or cancel upcoming services effortlessly.';

  @override
  String get lblNotification => "Notifications";

  @override
  String get lblUnAuthorized => "Demo user cannot be granted for this action";

  @override
  String get btnNext => "Next";

  @override
  String get lblViewAll => "View All";

  @override
  String get notAvailable => "Not Available";

  @override
  String get lblFavorite => "Favourite Services";

  @override
  String get lblChat => "Chat";

  @override
  String get getLocation => "Set";

  @override
  String get setAddress => "Set Address";

  @override
  String get requiredText => "This field is required";

  @override
  String get phnRequiredText => "Please enter mobile number";

  @override
  String get lblCall => "Call";

  @override
  String get lblRateHandyman => "Rate Handyman";

  @override
  String get msgForLocationOn => 'Your Location is turned on. Continue viewing services available from ALL areas?';

  @override
  String get msgForLocationOff => 'Your Location is turned off. Discover and find services available to your selected area.';

  @override
  String get lblEnterPhnNumber => "Enter your phone number";

  @override
  String get btnSendOtp => "Send OTP";

  @override
  String get lblLocationOff => "All services available";

  @override
  String get lblAppSetting => "App Setting";

  @override
  String get lblSubTotal => "Subtotal";

  @override
  String get lblImage => "Image";

  @override
  String get lblVideo => "Video";

  @override
  String get lblAudio => "Audio";

  @override
  String get lblChangePwdTitle => "Your new password must be different from previous used password";

  @override
  String get lblForgotPwdSubtitle => "A reset password link will be sent to the above entered email address";

  @override
  String get lblLoginTitle => "Hello Again";

  @override
  String get lblLoginSubTitle => "Welcome Back, You Have Been Missed For A Long Time";

  @override
  String get lblOrContinueWith => "Or Continue With";

  @override
  String get lblHelloUser => "Hello User !";

  @override
  String get lblSignUpSubTitle => "Create Your Account for Better Experience";

  @override
  String get lblStepper1Title => "Enter Detail Information";

  @override
  String get lblDateAndTime => "Date And Time:";

  @override
  String get chooseDateAndTime => "Choose Date And Time";

  @override
  String get lblYourAddress => "Your Address";

  @override
  String get lblEnterYourAddress => "Enter your address";

  @override
  String get lblUseCurrentLocation => "Use Current Location";

  @override
  String get lblEnterDescription => "Enter Description";

  @override
  String get lblPrice => "Price";

  @override
  String get lblTax => "Tax";

  @override
  String get lblDiscount => "Discount";

  @override
  String get lblAvailableCoupons => "Available Coupons";

  @override
  String get lblPrevious => "Previous";

  @override
  String get lblCoupon => "Coupon";

  @override
  String get lblEditYourReview => "Edit Your Review";

  @override
  String get lblTime => "Time";

  @override
  String get textProvider => "Provider";

  @override
  String get lblConfirmBooking => "Confirm Booking";

  @override
  String get lblConfirmMsg => 'Do you want to confirm this booking?';

  @override
  String get lblCancel => "Cancel";

  @override
  String get lblExpiryDate => "Expiry Date :";

  @override
  String get lblRemoveCoupon => "Remove Coupon";

  @override
  String get lblNoCouponsAvailable => "No Coupons available";

  @override
  String get lblStep1 => "Step 1";

  @override
  String get lblStep2 => "Step 2";

  @override
  String get lblBookingID => "Booking ID";

  @override
  String get lblDate => "Date";

  @override
  String get lblAboutHandyman => "About Handyman";

  @override
  String get lblAboutProvider => "About Provider";

  @override
  String get lblNotRatedYet => "You haven't rated yet";

  @override
  String get lblDeleteReview => "Delete Review";

  @override
  String get lblConfirmReviewSubTitle => "Do you want to delete this review?";

  @override
  String get lblConfirmService => "Do you want to hold this service?";

  @override
  String get lblConFirmResumeService => "Do you want to Resume this service?";

  @override
  String get lblEndServicesMsg => "Do you want to end this service?";

  @override
  String get lblCancelBooking => "Cancel Booking";

  @override
  String get lblStart => "Start";

  @override
  String get lblHold => "Hold";

  @override
  String get lblResume => "Resume";

  @override
  String get lblPayNow => "Pay Now";

  @override
  String get lblCheckStatus => "Check Status";

  @override
  String get lblID => "ID";

  @override
  String get lblNoBookingsFound => "No Bookings Found";

  @override
  String get lblCategory => "Category";

  @override
  String get lblYourComment => "Your Comment";

  @override
  String get lblIntroducingCustomerRating => "Introducing Customer Rating";

  @override
  String get lblSeeYourRatings => "See Your Ratings";

  @override
  String get lblFeatured => "Featured";

  @override
  String get lblNoServicesFound => "No services Found";

  @override
  String get lblGENERAL => "GENERAL";

  @override
  String get lblAboutApp => "About App";

  @override
  String get lblPurchaseCode => "Purchase Full Source Code";

  @override
  String get lblNoRateYet => "Currently you have not rated any services";

  @override
  String get lblMemberSince => "Member Since";

  @override
  String get lblFilterBy => "Filter By";

  @override
  String get lblClearFilter => "Clear Filter";

  @override
  String get lblNoReviews => "No Reviews";

  @override
  String get lblUnreadNotification => "Unread Notification";

  @override
  String get lblChoosePaymentMethod => "Choose Payment Method";

  @override
  String get lblNoPayments => "No Payments";

  @override
  String get lblPayWith => "Do you want to pay with";

  @override
  String get payWith => "Pay with";

  @override
  String get lblYourRating => "Your Rating";

  @override
  String get lblEnterReview => "Enter Your Review (Optional)";

  @override
  String get lblDelete => "Delete";

  @override
  String get lblDeleteRatingMsg => "Do you want to delete this Rating?";

  @override
  String get lblSelectRating => "Rating is required";

  @override
  String get lblNoServiceRatings => "No service Ratings";

  @override
  String get lblSearchFor => "Search for";

  @override
  String get lblRating => "Rating";

  @override
  String get lblAvailableAt => "Available At";

  @override
  String get lblRelatedServices => "Related Services";

  @override
  String get lblBookNow => "Book Now";

  @override
  String get lblWelcomeToHandyman => "Welcome To $APP_NAME";

  @override
  String get lblWalkThroughSubTitle => "$APP_NAME - On-Demand Home Services App with Complete Solution";

  @override
  String get textHandyman => "Handyman";

  @override
  String get lblChooseFromMap => "Choose From Map";

  @override
  String get lblDeleteAddress => "Delete Address";

  @override
  String get lblDeleteSunTitle => "Do you want to delete this address?";

  @override
  String get lblFaq => "FAQs";

  @override
  String get lblServiceFaq => "Service FAQs";

  @override
  String get lblLogoutTitle => "Oh No, You Are Leaving!";

  @override
  String get lblLogoutSubTitle => "Do you want to logout?";

  @override
  String get lblFeaturedProduct => "This is Featured Product";

  @override
  String get lblAlert => "Alert";

  @override
  String get lblOnBase => "On basis of";

  @override
  String get lblInvalidCoupon => "Coupon code is invalid";

  @override
  String get lblSelectCode => "Please Select Coupon code";

  @override
  String get lblBackPressMsg => "Press back again to exit app";

  @override
  String get lblHour => "hour";

  @override
  String get lblHelplineNumber => "Helpline Number";

  @override
  String get lblSubcategories => "Subcategories";

  @override
  String get lblAgree => "I agree to the";

  @override
  String get lblTermsOfService => "Terms of Service";

  @override
  String get lblWalkThrough0 => "$APP_NAME - $APP_NAME_TAG_LINE with Complete Solution";

  @override
  String get lblServiceTotalTime => "Service Total Time";

  @override
  String get lblDateTimeUpdated => "Your booking Date & Time has been completed successfully";

  @override
  String get lblSelectDate => "Please select Date Time";

  @override
  String get lblReasonCancelling => "Reason:";

  @override
  String get lblReasonRejecting => "Reason for rejecting this booking";

  @override
  String get lblFailed => "Reason why this booking is failed";

  @override
  String get lblNotDescription => "No Description Available";

  @override
  String get lblMaterialTheme => "Enable Material You Theme";

  @override
  String get lblServiceProof => "Service Proof";

  @override
  String get lblAndroid12Support => "This action will restart your app. Confirm?";

  @override
  String get lblOff => "Off";

  @override
  String get lblHr => "hr";

  @override
  String get lblSignInWithGoogle => "Sign In With Google";

  @override
  String get lblSignInWithOTP => "Sign In With OTP";

  @override
  String get lblDangerZone => "Danger Zone";

  @override
  String get lblDeleteAccount => "Delete Account";

  @override
  String get lblUnderMaintenance => "Under Maintenance...";

  @override
  String get lblCatchUpAfterAWhile => "Catch up after a while";

  @override
  String get lblId => "Id";

  @override
  String get lblMethod => "Method";

  @override
  String get lblStatus => "Status";

  @override
  String get lblPending => "Pending";

  @override
  String get confirmationRequestTxt => "Do you want to perform this Action?";

  @override
  String get lblDeleteAccountConformation => "Your account will be deleted permanently. Your Data will not be Restored Again.";

  @override
  String get lblAutoSliderStatus => "Auto Slider Status";

  @override
  String get lblPickAddress => "Pick address";

  @override
  String get lblUpdateDateAndTime => "Update Date and Time";

  @override
  String get lblRecheck => "Recheck";

  @override
  String get lblLoginAgain => "Please Login Again";

  @override
  String get lblUpdate => "Update";

  @override
  String get lblNewUpdate => "New Update";

  @override
  String get lblOptionalUpdateNotify => "Optional Update Notify";

  @override
  String get lblAnUpdateTo => "An Update to";

  @override
  String get lblIsAvailableWouldYouLike => "is available. Would you like to update?";

  @override
  String get lblRegisterAsPartner => "Register as Partner";

  @override
  String get lblSignInWithApple => "Sign in with Apple";

  @override
  String get lblWaitingForProviderApproval => "Waiting for Provider Approval";

  @override
  String get lblFree => "Free";

  @override
  String get lblAppleSignInNotAvailable => "Apple SignIn is not available for your device";

  @override
  String get lblTotalExtraCharges => "Total Extra Charges";

  @override
  String get lblWaitingForResponse => "Waiting for Response";

  @override
  String get lblAll => "All";

  @override
  String get noConversation => "No Conversation";

  @override
  String get noConversationSubTitle => "You didn't made any conversation yet. Please book a service to chat with a provider.";

  @override
  String get noBookingSubTitle => "Looks like you haven't book your order yet";

  @override
  String get myReviews => "My Reviews";

  @override
  String get noCategoryFound => "No Category Found";

  @override
  String get noProviderFound => "No Provider Found";

  @override
  String get createServiceRequest => "Create Service";

  @override
  String get chooseImages => "Choose Images";

  @override
  String get serviceDescription => "Service Description";

  @override
  String get addNewService => "Add New Service";

  @override
  String get newPostJobRequest => "Post New Job Request";

  @override
  String get postJobTitle => "Post Job Title";

  @override
  String get postJobDescription => "Post Job Description";

  @override
  String get services => "Services";

  @override
  String get myPostJobList => "My Custom Job Request";

  @override
  String get requestNewJob => "Request New Job";

  @override
  String get noNotifications => "No Notifications";

  @override
  String get noNotificationsSubTitle => "We'll notify you once we have something for you";

  @override
  String get noFavouriteSubTitle => "Your favourite services will appear here";

  @override
  String get termsConditionsAccept => "Please accept terms and conditions";

  @override
  String get disclaimer => "Disclaimer";

  @override
  String get disclaimerContent => "You will be asked for payment once your booking is completed.";

  @override
  String get inputMustBeNumberOrDigit => 'Input must be number or digit';

  @override
  String get requiredAfterCountryCode => 'required after country code';

  @override
  String get selectedOtherBookingTime => 'Selected booking time is already passed. Please select another time.';

  @override
  String get myServices => 'My Services';

  @override
  String get doYouWantToAssign => 'Do you want to assign';

  @override
  String get bidPrice => 'Bid Price';

  @override
  String get accept => 'Accept';

  @override
  String get price => 'Price';

  @override
  String get remove => 'Remove';

  @override
  String get add => 'Add';

  @override
  String get save => 'Save';

  @override
  String get createPostJobWithoutSelectService => 'You can\'t create post job without selecting service';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get pleaseAddImage => 'Please Add Image';

  @override
  String get selectedBookingTimeIsAlreadyPassed => 'Selected booking time is already passed. Please select another time.';

  @override
  String get jobPrice => 'Job Price';

  @override
  String get estimatedPrice => 'Estimated Price';

  @override
  String get bidder => 'Bidder List';

  @override
  String get assignedProvider => 'Assigned Provider';

  @override
  String get myPostDetail => 'My Post Detail';

  @override
  String get thankYou => 'Thank you!';

  @override
  String get bookingConfirmedMsg => 'Your booking is confirmed.';

  @override
  String get goToHome => 'Go to Home';

  @override
  String get goToReview => 'Go to Review';

  @override
  String get noServiceAdded => 'No Service Added';

  @override
  String get noPostJobFound => 'No Post Job Found';

  @override
  String get noPostJobFoundSubtitle => 'When you post your job, every Provider will notified, and you can choose your desired Provider to get the job done.';

  @override
  String get pleaseEnterValidOTP => 'Please enter Valid OTP';

  @override
  String get confirmOTP => 'تأكيد كلمة المرور لمرة واحدة';

  @override
  String get sendingOTP => 'إرسال كلمة المرور لمرة واحدة';

  @override
  String get pleaseSelectDifferentSlotThenPrevious => 'Please select different slot then previous';

  @override
  String get pleaseSelectTheSlotsFirst => 'Please select the slots first';

  @override
  String get editTimeSlotsBooking => 'Edit Time Slots Booking';

  @override
  String get availableSlots => 'Available Slots';

  @override
  String get noTimeSlots => 'No Time Slots';

  @override
  String get bookingDateAndSlot => 'Booking Date & Slot';

  @override
  String get extraCharges => 'Extra Charges';

  @override
  String get chatCleared => 'Chat Cleared';

  @override
  String get clearChat => 'Clear chat';

  @override
  String get jobRequestSubtitle => "Didn't find your service? Don't worry, You can post your requirements.";

  @override
  String get verified => 'Verified';

  @override
  String get theEnteredCodeIsInvalidPleaseTryAgain => 'The entered code is invalid, please try again';

  @override
  String get otpCodeIsSentToYourMobileNumber => 'OTP Code is sent to your mobile number';

  @override
  String get yourPaymentFailedPleaseTryAgain => 'Your payment failed please try again';

  @override
  String get yourPaymentHasBeenMadeSuccessfully => 'Your payment has been made successfully';

  @override
  String get transactionFailed => 'Transaction Failed';

  @override
  String get lblStep3 => "Step 3";

  @override
  String get lblAvailableOnTheseDays => "Available On These Days";

  @override
  String get internetNotAvailable => 'Your internet appears to be offline';

  @override
  String get pleaseTryAgain => 'Please try again';

  @override
  String get somethingWentWrong => 'Something Went Wrong';

  @override
  String get postJob => 'Post Job';

  @override
  String get package => 'Package';

  @override
  String get frequentlyBoughtTogether => 'Frequently Bought Together';

  @override
  String get endOn => 'Ends On';

  @override
  String get buy => 'Buy';

  @override
  String get includedServices => 'Included Services';

  @override
  String get includedInThisPackage => 'Included in this Package';

  @override
  String get lblInvalidTransaction => 'Invalid Transaction';

  @override
  String get getTheseServiceWithThisPackage => 'You will get these services with this package';

  @override
  String get lblNotValidUser => 'You are not a valid User';

  @override
  String get lblSkip => 'Skip';

  @override
  String get lblChangeCountry => 'Change Country';

  @override
  String get lblTimeSlotNotAvailable => 'This Slot is not available';

  @override
  String get lblAdd => 'add';

  @override
  String get lblThisService => 'this service';

  @override
  String get lblYourCurrenciesNotSupport => 'Your Currencies doesn\'t support CinetPay';

  @override
  String get lblSignInFailed => 'Sign in failed';

  @override
  String get lblUserCancelled => 'User cancelled';

  @override
  String get lblTransactionCancelled => 'Transaction cancelled';

  @override
  String get lblExample => 'Example';

  @override
  String get lblCheckOutWithCinetPay => 'Checkout with CinetPay';

  @override
  String get lblLocationPermissionDenied => 'Location permissions are denied.';

  @override
  String get lblLocationPermissionDeniedPermanently => 'Location permissions are permanently denied, we cannot request permissions.';

  @override
  String get lblEnableLocation => 'Please make sure Location services are enabled.';

  @override
  String get lblNoUserFound => 'No User Found';

  @override
  String get lblUserNotCreated => 'User Not Created';

  @override
  String get lblTokenExpired => 'Token Expired';

  @override
  String get lblConfirmationForDeleteMsg => 'Do you want to delete the message?';

  @override
  String get favouriteProvider => 'Favourite Provider';

  @override
  String get noProviderFoundMessage => 'Your favourite providers will appear here';

  @override
  String get personalInfo => 'Personal Info';

  @override
  String get essentialSkills => 'Essential Skills';

  @override
  String get knownLanguages => 'Known Languages';

  @override
  String get authorBy => 'Author By';

  @override
  String get views => 'Views';

  @override
  String get blogs => 'Blogs';

  @override
  String get noBlogsFound => 'No Blogs Found';

  @override
  String get requestInvoice => 'Request Invoice';

  @override
  String get invoiceSubTitle => 'Enter the email address where you wish to receive your invoice';

  @override
  String get sentInvoiceText => 'Please check your email we have sent invoice on your email.';

  @override
  String get send => 'Send';

  @override
  String get published => 'Published';

  @override
  String get clearChatMessage => 'Do you want to clear this chat?';

  @override
  String get deleteMessage => 'Do you want to delete?';

  @override
  String get accepted => 'Accepted';

  @override
  String get onGoing => 'On Going';

  @override
  String get inProgress => 'In Progress';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get rejected => 'Rejected';

  @override
  String get failed => 'Failed';

  @override
  String get completed => 'Completed';

  @override
  String get pendingApproval => 'Pending Approval';

  @override
  String get waiting => 'Waiting';

  @override
  String get paid => 'Paid';

  @override
  String get advancePaid => 'Advance Paid';

  @override
  String get insufficientBalanceMessage => 'You have an insufficient balance in your wallet. Please choose another method.';

  @override
  String get cinetPayNotSupportedMessage => "CinetPay isn't supported by your Currencies";

  @override
  String get loading => 'Loading..';

  @override
  String get walletBalance => 'Wallet Balance';

  @override
  String get payAdvance => 'Pay Advance';

  @override
  String get advancePaymentMessage => 'Make an advance payment to complete a booking';

  @override
  String get advancePayAmount => 'Advance Amount';

  @override
  String get remainingAmount => 'Remaining Amount';

  @override
  String get advancePayment => 'Pay Advance';

  @override
  String get withExtraAndAdvanceCharge => 'With Extra charge and Advance payment';

  @override
  String get withExtraCharge => 'With Extra Charge';

  @override
  String get min => 'min';

  @override
  String get hour => 'hour';

  @override
  String get customerRatingMessage => 'Tell others what you think';

  @override
  String get paymentHistory => 'Payment History';

  @override
  String get message => 'Message';

  @override
  String get wallet => 'Wallet';

  @override
  String get payWithFlutterWave => 'Pay With Flutterwave';

  @override
  String get goodMorning => 'Good Morning';

  @override
  String get goodAfternoon => 'Good Afternoon';

  @override
  String get goodEvening => 'Good Evening';

  @override
  String get invalidURL => 'Invalid URL';

  @override
  String get use24HourFormat => 'Use 24-hour format?';

  @override
  String get email => 'Email';

  @override
  String get badRequest => 'Bad Request';

  @override
  String get forbidden => 'Forbidden';

  @override
  String get pageNotFound => 'Page Not Found';

  @override
  String get tooManyRequests => 'Too Many Requests';

  @override
  String get internalServerError => 'Internal Server Error';

  @override
  String get badGateway => 'Bad Gateway';

  @override
  String get serviceUnavailable => 'Service Unavailable';

  @override
  String get gatewayTimeout => 'Gateway Timeout';

  @override
  String get pleaseWait => 'Please wait';

  @override
  String get externalWallet => 'External Wallet';

  @override
  String get userNotFound => 'User not found';

  @override
  String get requested => 'Requested';

  @override
  String get assigned => 'Assigned';

  @override
  String get reload => 'Reload';

  @override
  String get lblStripeTestCredential => 'Testing Credential cannot pay more then 500';

  @override
  String get noDataFoundInFilter => 'Choose the best filter criteria to get the best results';

  @override
  String get addYourCountryCode => 'Add your country code';

  @override
  String get help => 'Help';

  @override
  String get couponCantApplied => "This coupon can't be applied";

  @override
  String get priceAmountValidationMessage => 'Price amount should be grater than 0';

  @override
  String get pleaseWaitWhileWeLoadChatDetails => "Please wait while we load chat details...";

  @override
  String get isNotAvailableForChat => "is not available for chat";

  @override
  String get connectWithFirebaseForChat => "Connect with firebase for chat";

  @override
  String get closeApp => 'Close App';

  @override
  String get providerAddedToFavourite => 'Provider added to Favourite list';

  @override
  String get providerRemovedFromFavourite => 'Provider removed from Favourite list';

  @override
  String get provideValidCurrentPasswordMessage => 'You must provide a valid current password';

  @override
  String get copied => 'Copied';

  @override
  String get copyMessage => 'Copy Message';

  @override
  String get messageDelete => 'Delete Message';

  @override
  String get pleaseChooseAnyOnePayment => 'Please choose any one payment method';

  @override
  String get myWallet => 'My Wallet';

  @override
  String get balance => 'Balance';

  @override
  String get topUpWallet => 'Top-up Wallet';

  @override
  String get topUpAmountQuestion => 'What amount would you prefer to top up with?';

  @override
  String get paymentMethod => 'Payment Method';

  @override
  String get selectYourPaymentMethodToAddBalance => 'Select your payment method to add balance';

  @override
  String get proceedToTopUp => 'Proceed to top-up';

  @override
  String get serviceAddedToFavourite => 'Service added to Favourite list';

  @override
  String get serviceRemovedFromFavourite => 'Service removed from Favourite list';

  @override
  String get firebaseRemoteCannotBe => 'Firebase remote cannot be connected';

  @override
  String get search => 'Search';

  @override
  String get close => 'Close';

  @override
  String get totalAmountShouldBeMoreThan => 'Total amount should be more than';

  @override
  String get totalAmountShouldBeLessThan => 'Total amount should be less than';

  @override
  String get doYouWantToTopUpYourWallet => 'Do you want to Top Up your wallet now?';

  @override
  String get chooseYourLocation => 'Choose your location';

  @override
  String get connect => 'Connect';

  @override
  String get transactionId => 'Transaction ID';

  @override
  String get at => 'at';

  @override
  String get appliedTaxes => 'Applied Taxes';

  @override
  String get accessDeniedContactYourAdmin => 'Access denied. Contact your administrator for assistance.';

  @override
  String get yourWalletIsUpdated => 'Your wallet is updated!';

  @override
  String get by => 'by';

  @override
  String get noPaymentMethodFound => 'No Payment Method Found';

  @override
  String get theAmountShouldBeEntered => 'The amount should be entered';

  @override
  String get walletHistory => 'Wallet History';

  @override
  String get debit => 'Debit';

  @override
  String get credit => 'Credit';

  @override
  String get youCannotApplyThisCoupon => 'You cannot apply this coupon';

  @override
  String get basedOn => 'Based on';

  @override
  String get serviceStatusPicMessage => 'Please ensure you pick at least one Booking Status';

  @override
  String get clearFilter => 'Clear Filter';

  @override
  String get bookingStatus => 'Booking Status';

  @override
  String get addOns => 'Add-ons';

  @override
  String get serviceAddOns => 'Service Add-ons';

  @override
  String get turnOn => 'Turn On';

  @override
  String get turnOff => 'Turn Off';

  @override
  String get serviceVisitType => 'Service Visit Type';

  @override
  String get thisServiceIsOnlineRemote => 'This service will be completed Online/Remotely.';

  @override
  String get deleteMessageForAddOnService => 'Do you want to remove this Add-on Service?';

  @override
  String get confirmation => 'Confirmation!';

  @override
  String get pleaseNoteThatAllServiceMarkedCompleted => 'Please Note That all service add-ons marked as completed!';

  @override
  String get writeHere => 'Write Here';

  @override
  String get isAvailableGoTo => 'is available. Go to Play Store and Download the New Version of the App.';

  @override
  String get later => 'Later';

  @override
  String get whyChooseMe => 'Why Choose Me?';

  @override
  String get useThisCodeToGet => 'Use this code to get';

  @override
  String get off => 'off';

  @override
  String get applied => 'Applied';

  @override
  String get coupons => 'Coupons';

  @override
  String get handymanList => 'Handyman List';

  @override
  String get noHandymanFound => 'No Handyman Found';

  @override
  String get back => 'Back';

  @override
  String get team => 'Team';

  @override
  String get whyChooseMeAs => 'Why Choose Me as Your Trusted Service Provider';

  @override
  String get reason => 'Reason';

  @override
  String get pleaseEnterAddressAnd => 'Please Enter Address and Booking Date & Slot';

  @override
  String get pleaseEnterYourAddress => 'Please Enter Your Address';

  @override
  String get pleaseSelectBookingDate => 'Please Select Booking Date & Slot';

  @override
  String get doYouWantTo => 'Do you want to remove this coupon?';

  @override
  String get chooseDateTime => 'Choose Date & Time';

  @override
  String get airtelMoneyPayment => 'Airtel Money Payment';

  @override
  String get recommendedForYou => 'Recommended for you';

  @override
  String get paymentSuccess => 'Payment Success';

  @override
  String get redirectingToBookings => 'Redirecting to bookings..';

  @override
  String get transactionIsInProcess => 'Transaction is in process...';

  @override
  String get pleaseCheckThePayment => 'Please check the payment request is sent to your number';

  @override
  String get enterYourMsisdnHere => 'Enter your msisdn here';

  @override
  String get theTransactionIsStill => 'The transaction is still processing and is in ambiguous state. Please do the transaction enquiry to fetch the transaction status.';

  @override
  String get transactionIsSuccessful => 'Transaction is successful';

  @override
  String get incorrectPinHasBeen => 'Incorrect Pin has been entered';

  @override
  String get theUserHasExceeded => 'The User has exceeded their wallet allowed transaction limit';

  @override
  String get theAmountUserIs => 'The amount User is trying to transfer is less than the minimum amount allowed';

  @override
  String get userDidnTEnterThePin => "User didn't enter the pin";

  @override
  String get transactionInPendingState => 'Transaction in pending state. Please check after sometime';

  @override
  String get userWalletDoesNot => 'User wallet does not have enough money to cover the payable amount';

  @override
  String get theTransactionWasRefused => 'The transaction was refused';

  @override
  String get thisIsAGeneric => 'This is a generic refusal that has several possible causes';

  @override
  String get payeeIsAlreadyInitiated => 'Payee is already initiated for churn or barred or not registered on Airtel Money platform';

  @override
  String get theTransactionWasTimed => 'The transaction was timed out.';

  @override
  String get theTransactionWasNot => 'The transaction was not found.';

  @override
  String get xSignatureAndPayloadDid => 'x-signature and payload did not match';

  @override
  String get encryptionKeyHasBeen => 'Encryption key has been fetched successfully';

  @override
  String get couldNotFetchEncryption => 'Could not fetch encryption key';

  @override
  String get transactionHasBeenExpired => 'Transaction has been expired';

  @override
  String get ambiguous => 'Ambiguous';

  @override
  String get success => 'Success';

  @override
  String get incorrectPin => 'Incorrect Pin';

  @override
  String get exceedsWithdrawalAmountLimitS => 'Exceeds withdrawal amount limit(s) / Withdrawal amount limit exceeded';

  @override
  String get invalidAmount => 'Invalid Amount';

  @override
  String get transactionIdIsInvalid => 'Transaction ID is invalid';

  @override
  String get inProcess => 'In process';

  @override
  String get notEnoughBalance => 'Not enough balance';

  @override
  String get refused => 'Refused';

  @override
  String get doNotHonor => 'Do not honor';

  @override
  String get transactionNotPermittedTo => 'Transaction not permitted to Payee';

  @override
  String get transactionTimedOut => 'Transaction Timed Out';

  @override
  String get transactionNotFound => 'Transaction Not Found';

  @override
  String get forBidden => 'Forbidden';

  @override
  String get successfullyFetchedEncryptionKey => 'Successfully fetched Encryption Key';

  @override
  String get errorWhileFetchingEncryption => 'Error while fetching encryption key';

  @override
  String get transactionExpired => 'Transaction Expired';

  @override
  String get verifyEmail => 'Verify Email';

  @override
  String get minRead => 'min read';

  @override
  String get loadingChats => 'Loading chats...';

  @override
  String get monthly => 'Monthly';

  @override
  String get noCouponsAvailableMsg => 'No Coupons at the Moment. Keep Checking Back for Exclusive Offers!';

  @override
  String get refundPolicy => 'Refund Policy';

  @override
  String get chooseAnyOnePayment => 'Choose any one payment method first';

  @override
  String get january => 'January';

  @override
  String get february => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'October';

  @override
  String get november => 'November';

  @override
  String get december => 'December';

  @override
  String get monthName => 'Month Name';

  @override
  String get mon => 'Mon';

  @override
  String get tue => 'Tue';

  @override
  String get wed => 'Wed';

  @override
  String get thu => 'Thu';

  @override
  String get fri => 'Fri';

  @override
  String get sat => 'Sat';

  @override
  String get sun => 'Sun';

  @override
  String get weekName => 'Week Name';

  @override
  String get removeThisFile => 'Remove This File';

  @override
  String get areYouSureWantToRemoveThisFile => 'Do you want to remove this file?';

  @override
  String get sendMessage => 'Send Message';

  @override
  String get youAreNotConnectedWithChatServer => 'Connect to Chat Server';

  @override
  String get NotConnectedWithChatServerMessage => 'You are not connected to the chat server. Tap the button below to connect and start chatting';

  @override
  String get sentYouAMessage => 'sent you a message';

  @override
  String get pushNotification => 'Push Notification';

  @override
  String get yourBooking => 'Your Booking';

  @override
  String get featuredServices => 'Featured Services';

  @override
  String get postYourRequestAnd => 'Post your request, and we\'ll \ndo our best to fulfill it';

  @override
  String get newRequest => 'New Request';

  @override
  String get upcomingBooking => 'Upcoming Booking';

  @override
  String get theUserHasDenied => 'The user has denied the use of speech recognition';

  @override
  String get helloGuest => 'Hello Guest';

  @override
  String get eGCleaningPlumberPest => 'e.g. cleaning, plumber, pest control';

  @override
  String get ifYouDidnTFind => "If you didn't find our service, don't worry! You can easily post your request.";

  @override
  String get popularServices => 'Popular Services';

  @override
  String get canTFindYourServices => "Can't find your services?";

  @override
  String get trackProviderLocation => 'Track Provider Location';

  @override
  String get trackHandymanLocation => 'Track Handyman Location';

  @override
  String get handymanLocation => 'Handyman Location';

  @override
  String get providerLocation => 'Provider Location';

  @override
  String get lastUpdatedAt => 'Last updated at:';

  @override
  String get track => 'Track';

  @override
  String get handymanReached => 'Handyman Reached? Click to start';

  @override
  String get providerReached => 'Provider Reached? Click to start';

  @override
  String get addBank => "Add bank";

  @override
  String get bankList => "Bank List";

  @override
  String get lbldefault => "Default";

  @override
  String get setAsDefault => "Set as default";

  @override
  String get aadharNumber => "Aadhar Number";

  @override
  String get panNumber => "PAN Number";

  @override
  String get lblPleaseEnterAccountNumber => "Please Enter Account Number";

  @override
  String get lblAccountNumberMustContainOnlyDigits => "Account number must contain only digits";

  @override
  String get lblAccountNumberMustBetween11And16Digits => "Account number must be between 11 and 16 digits";

  @override
  String get noBankDataTitle => "No Bank Data Found";

  @override
  String get noBankDataSubTitle => "You didn't add bank yet";

  @override
  String get lblBankDetails => "Bank Details";

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get deleteBankTitle => 'Do you want to delete this bank?';

  @override
  String get lblEdit => 'Edit';

  @override
  String get bankName => "Bank Name";

  @override
  String get accountNumber => "Account number";

  @override
  String get iFSCCode => "IFSC code";

  @override
  String get availableBalance => "Available Balance";

  @override
  String get withdraw => "Withdraw";

  @override
  String get successful => 'Successful';

  @override
  String get yourWithdrawalRequestHasBeenSuccessfullySubmitted => 'Your withdrawal request has been successfully submitted.';

  @override
  String get eg3000 => 'eg" 3000"';

  @override
  String get chooseBank => "Choose Bank";

  @override
  String get egCentralNationalBank => 'eg" central national bank"';

  @override
  String get topUp => "Top-up";

  @override
  String get pleaseAddLessThanOrEqualTo => "Please add less than or equal to";

  @override
  String get lblEnterAmount => "Enter Amount";

  @override
  String get withdrawRequest => "Withdraw Request";

  @override
  String get btnSave => 'Save';

  @override
  String get fullNameOnBankAccount => 'Full name on bank account';

  @override
  String get packageIsExpired => 'Package is expired';

  @override
  String get bookPackage => 'Book Package';

  @override
  String get packageDescription => 'Package Description';

  @override
  String get packagePrice => 'Package Price';

  @override
  String get online => 'Online';

  @override
  String get noteAddressIsNot => 'Note: Address is not required for remote services.';

  @override
  String get wouldYouLikeTo => 'Would you like to proceed and confirm this booking?';

  @override
  String get packageName => 'Package Name';

  @override
  String get feeAppliesForCancellations => 'fee applies for cancellations made within';

  @override
  String get a => 'A';

  @override
  String get byConfirmingYouAgree => 'By confirming, you agree to our';

  @override
  String get and => 'and';

  @override
  String get areYouSureYou => 'Are you sure you want to cancel? A cancellation fee may apply based on your service price';

  @override
  String get totalCancellationFee => 'Total cancellation fee';

  @override
  String get goBack => 'Go Back';

  @override
  String get bookingCancelled => 'Booking Cancelled';

  @override
  String get yourBookingHasBeen => 'Your booking has been successfully canceled. Applicable refund will be processed within 24 hours';

  @override
  String get noteCheckYourBooking => 'Note: Check your booking history for refund details';

  @override
  String get cancelledReason => 'Cancelled reason';

  @override
  String get refundPaymentDetails => 'Refund Payment Details';

  @override
  String get refundOf => 'Refund of';

  @override
  String get refundAmount => 'Refund Amount';

  @override
  String get cancellationFee => 'Cancellation Fee';

  @override
  String get advancedPayment => 'Advanced payment';

  @override
  String get hoursOfTheScheduled => 'hours of the scheduled service';

  @override
  String get open => 'OPEN';

  @override
  String get closed => 'CLOSED';

  @override
  String get createBy => 'Create by';

  @override
  String get repliedBy => 'Replied by';

  @override
  String get closedBy => 'Closed by';

  @override
  String get helpDesk => 'Help Desk';

  @override
  String get addNew => 'Add New';

  @override
  String get queryYet => 'Query Yet';

  @override
  String get toSubmitYourProblems => 'To submit your problems simply press add button and explain your concern';

  @override
  String get noRecordsFoundFor => 'No records found for';

  @override
  String get queries => 'queries.';

  @override
  String get noActivityYet => 'No Activity Yet';

  @override
  String get noRecordsFound => 'No records found';

  @override
  String get reply => 'Reply';

  @override
  String get eGDuringTheService => 'e.g. During the service, the furniture was accidentally damaged.';

  @override
  String get doYouWantClosedThisQuery => 'Do you want closed this query';

  @override
  String get markAsClosed => 'Mark as Closed';

  @override
  String get youCanMarkThis => 'You can mark this as closed if you are satisfied with our answer';

  @override
  String get subject => 'Subject';

  @override
  String get eGDamagedFurniture => 'e.g. Damaged furniture';

  @override
  String get closedOn => 'Closed on:';

  @override
  String get on => 'on';

  @override
  String get showMessage => 'Show Message';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get chooseAction => 'Choose Action';

  @override
  String get chooseImage => 'Choose Image';

  @override
  String get noteYouCanUpload => 'Note: You can upload image with \'jpg\', \'png\', \'jpeg\' extensions & you can select only one image';

  @override
  String get removeImage => 'Remove Image';

  @override
  String get advancedRefund => 'Advanced Refund';

  @override
  String get lblService => 'Service';

  @override
  String get dateRange => 'Date Range';

  @override
  String get paymentType => 'Payment Type';

  @override
  String get reset => 'Reset';

  @override
  String get noStatusFound => 'No Status Found';

  @override
  String get selectStartDateEndDate => 'Select start date & end date';

  @override
  String get handymanNotFound => 'Handyman Not Found';

  @override
  String get providerNotFound => 'Provider Not Found';

  @override
  String get rateYourExperience => 'Rate Your Experience';

  @override
  String get weValueYourFeedback => 'We value your feedback! Please rate your recent experience with our service';

  @override
  String get viewStatus => 'View Status';

  @override
  String get paymentInfo => 'Payment Info';

  @override
  String get mobile => 'Mobile:';

  @override
  String get to => 'to';

  @override
  String get chooseYourDateRange => 'Choose your Date Range';

  @override
  String get asHandyman => 'As Handyman';

  @override
  String get passwordLengthShouldBe => 'Password length should be 8 to 12 characters.';

  @override
  String get cash => "Cash";

  @override
  String get bank => "Bank";

  @override
  String get razorPay => "RazorPay";

  @override
  String get payPal => "PayPal";

  @override
  String get stripe => "Stripe";

  @override
  String get payStack => "PayStack";

  @override
  String get flutterWave => "FlutterWave";

  @override
  String get paytm => "Paytm";

  @override
  String get airtelMoney => "Airtel Money";

  @override
  String get cinet => "Cinet";

  @override
  String get midtrans => "Midtrans";

  @override
  String get sadadPayment => "Sadad";

  @override
  String get phonePe => "PhonePe";

  @override
  String get inAppPurchase => "In-App Purchase";

  @override
  String get pix => "Pix";

  @override
  String get chooseWithdrawalMethod => "Choose Withdrawal Method";
}