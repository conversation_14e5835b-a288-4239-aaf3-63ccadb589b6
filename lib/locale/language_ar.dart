import 'package:booking_system_flutter/locale/languages.dart';

import '../utils/configs.dart';

class LanguageAr extends BaseLanguage {
  @override
  String get walkTitle1 => 'إنشاء وإعداد حسابك';

  @override
  String get walkTitle2 => 'تصفح وحجز الخدمات';

  @override
  String get walkTitle3 => 'تتبع وإدارة الحجوزات الخاصة بك';

  @override
  String get getStarted => 'البدء';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signUp => 'انشئ حساب';

  @override
  String get hintFirstNameTxt => 'أدخل اسمك الأول';

  @override
  String get hintLastNameTxt => 'أدخل اسم العائلة';

  @override
  String get hintContactNumberTxt => 'أدخل رقم الاتصال الخاص بك';

  @override
  String get hintEmailAddressTxt => 'أدخل عنوان بريدك الالكتروني';

  @override
  String get hintUserNameTxt => 'اسم المستخدم';

  @override
  String get hintPasswordTxt => 'ادخل رقمك السري';

  @override
  String get hintReenterPasswordTxt => 'إعادة إدخال كلمة المرور الخاصة بك';

  @override
  String get confirm => 'تاكيد';

  @override
  String get hintEmailTxt => 'أدخل بريدك الإلكتروني';

  @override
  String get forgotPassword => 'هل نسيت كلمة السر؟';

  @override
  String get alreadyHaveAccountTxt => 'هل لديك حساب';

  @override
  String get rememberMe => 'تذكرنى';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get dashboard => 'الرئيسية';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get camera => 'آلة تصوير';

  @override
  String get language => 'لغة';

  @override
  String get appTheme => 'موضوع التطبيق';

  @override
  String get bookingHistory => 'حجز التاريخ';

  @override
  String get rateUs => 'قيمنا';

  @override
  String get termsCondition => 'الشروط والحالة';

  @override
  String get helpSupport => 'ساعد لدعم';

  @override
  String get privacyPolicy => 'سياسة خاصة';

  @override
  String get about => 'عن';

  @override
  String get logout => 'تسجيل خروج';

  @override
  String get chooseTheme => 'اختر موضوع التطبيق';

  @override
  String get selectCountry => 'حدد الدولة';

  @override
  String get selectState => 'اختر مدينة';

  @override
  String get selectCity => 'اختر مدينة';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get passwordNotMatch => "كلمة السر غير متطابقة";

  @override
  String get doNotHaveAccount => "ليس لديك حساب؟";

  @override
  String get hintNewPasswordTxt => "أدخل كلمة المرور الجديدة";

  @override
  String get hintOldPasswordTxt => "أدخل كلمة المرور القديمة";

  @override
  String get hintAddress => 'أدخل عنوانك';

  @override
  String get lblGallery => "صالة عرض";

  @override
  String get yourReview => "مراجعتك";

  @override
  String get review => "إعادة النظر";

  @override
  String get hintDescription => "أدخل الوصف الخاص بك";

  @override
  String get lblApply => "تطبيق";

  @override
  String get bookTheService => "حجز الخدمة";

  @override
  String get contactAdmin => "يرجى الاتصال مع المسؤول";

  @override
  String get allServices => 'جميع الخدمات';

  @override
  String get duration => 'مدة';

  @override
  String get hourly => "بالساعة";

  @override
  String get payment => "قسط";

  @override
  String get done => "منتهي";

  @override
  String get totalAmount => 'المبلغ الإجمالي';

  @override
  String get applyCoupon => 'تطبيق القسيمة';

  @override
  String get priceDetail => 'تفاصيل السعر';

  @override
  String get home => 'الصفحة الرئيسية';

  @override
  String get category => 'فئة';

  @override
  String get booking => 'الحجز';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get lblAlertBooking => 'هل تريد حجز الخدمة؟';

  @override
  String get serviceName => 'اسم الخدمة';

  @override
  String get service => 'خدمة';

  @override
  String get lblCancelReason => 'الرجاء إدخال السبب لإلغاء حجز الخدمة هذه.';

  @override
  String get enterReason => "أدخل السبب هنا";

  @override
  String get noDataAvailable => 'لا تتوافر بيانات';

  @override
  String get lblOk => 'نعم';

  @override
  String get paymentDetail => 'تفاصيل الدفع';

  @override
  String get paymentStatus => 'حالة السداد';

  @override
  String get viewDetail => 'عرض التفاصيل';

  @override
  String get appThemeLight => 'اضاءة';

  @override
  String get appThemeDark => 'داكن';

  @override
  String get appThemeDefault => 'النظام الافتراضي';

  @override
  String get markAsRead => "اشر عليها بانها قرات";

  @override
  String lblYes = 'نعم';

  @override
  String lblNo = 'لا';

  @override
  String btnRate = "قيم الآن";

  @override
  String btnSubmit = "ارسال";

  @override
  String get walkThrough1 => 'قم بالتسجيل أو تسجيل الدخول إلى حسابك باستخدام البريد الإلكتروني الخاص بك أو ملفات تعريف الوسائط الاجتماعية. يضمن إكمال ملف التعريف الخاص بك تجربة حجز سلسة.';

  @override
  String get walkThrough2 => 'اكتشف مجموعة واسعة من الخدمات المتوفرة في منطقتك. حدد خدمة، واختر فترة زمنية مناسبة، وقدم تفاصيل موقعك لحجز الخدمة بسرعة وسهولة.';

  @override
  String get walkThrough3 => 'تتبع حالة الخدمة الخاصة بك في الوقت الحقيقي. عرض وإدارة حجوزاتك الحالية والسابقة. إعادة جدولة أو إلغاء الخدمات القادمة دون عناء.';

  @override
  String lblNotification = "إشعارات";

  @override
  String get lblUnAuthorized => "لا يمكن منح المستخدم التجريبي لهذا الإجراء";

  @override
  String get btnNext => "التالي";

  @override
  String get lblViewAll => "عرض الكل";

  @override
  String get notAvailable => "غير متوفر";

  @override
  String get lblFavorite => "الخدمات المفضلة";

  @override
  String get lblChat => "دردشة";

  @override
  String get getLocation => "الحصول على الموقع";

  @override
  String get setAddress => "تعيين العنوان";

  @override
  String get requiredText => "هذه الخانة مطلوبه";

  @override
  String get phnRequiredText => "الرجاء إدخال رقم الهاتف المحمول";

  @override
  String get lblCall => "اتصال";

  @override
  String get lblRateHandyman => "مكالمة";

  @override
  String get msgForLocationOn => 'يتم تشغيل موقعك. استمر في عرض الخدمات المتاحة من جميع المجالات؟';

  @override
  String get msgForLocationOff => 'يتم إيقاف تشغيل موقعك. اكتشف وابحث عن الخدمات المتاحة لمنطقتك المختارة.';

  @override
  String get lblEnterPhnNumber => "أدخل رقم هاتفك";

  @override
  String get btnSendOtp => "إرسال OTP.";

  @override
  String get lblLocationOff => "جميع الخدمات المتاحة";

  @override
  String get lblAppSetting => "إعداد التطبيق";

  @override
  String get lblSubTotal => "قيمة الضريبة";

  @override
  String get lblImage => "صورة";

  @override
  String get lblVideo => "فيديو";

  @override
  String get lblAudio => "صوتي";

  @override
  String get lblChangePwdTitle => "يجب أن تكون كلمة المرور الجديدة مختلفة عن كلمة المرور السابقة المستخدمة";

  @override
  String get lblForgotPwdSubtitle => "سيتم إرسال رابط إعادة تعيين كلمة المرور إلى عنوان البريد الإلكتروني الذي تم إدخاله أعلاه";

  @override
  String get lblLoginTitle => "مرحبا مجددا ";

  @override
  String get lblLoginSubTitle => "مرحبًا بك مرة أخرى ، لقد فاتتك لفترة طويلة";

  @override
  String get lblOrContinueWith => "أو متابعة";

  @override
  String get lblHelloUser => "مرحبا المستخدم!";

  @override
  String get lblSignUpSubTitle => 'قم بإنشاء حسابك لتجربة أفضل';

  @override
  String get lblStepper1Title => "أدخل معلومات التفاصيل";

  @override
  String get lblDateAndTime => ":التاريخ و الوقت";

  @override
  String get chooseDateAndTime => 'اختر التاريخ والوقت';

  @override
  String get lblYourAddress => "عنوانك";

  @override
  String get lblEnterYourAddress => "أدخل عنوانك";

  @override
  String get lblUseCurrentLocation => "استخدام الموقع الحالي";

  @override
  String get lblEnterDescription => "أدخل الوصف";

  @override
  String get lblPrice => "السعر";

  @override
  String get lblTax => "ضريبة";

  @override
  String get lblDiscount => "خصم";

  @override
  String get lblAvailableCoupons => "كوبونات المتاحة";

  @override
  String get lblPrevious => "السابق";

  @override
  String get lblCoupon => "كوبون";

  @override
  String get lblEditYourReview => "تحرير تقييمك";

  @override
  String get lblTime => "الوقت";

  @override
  String get textProvider => "مزود";

  @override
  String get lblConfirmBooking => "تأكيد الحجز";

  @override
  String get lblConfirmMsg => 'هل تريد تأكيد هذا الحجز؟';

  @override
  String get lblCancel => "الغاء";

  @override
  String get lblExpiryDate => "تاريخ الانتهاء :";

  @override
  String get lblRemoveCoupon => "إزالة القسيمة";

  @override
  String get lblNoCouponsAvailable => "لا كوبونات متاحة";

  @override
  String get lblStep1 => "الخطوة 1";

  @override
  String get lblStep2 => "الخطوة 2";

  @override
  String get lblBookingID => "معرف الحجز";

  @override
  String get lblDate => "تاريخ";

  @override
  String get lblAboutHandyman => "حول العمل اليدوي";

  @override
  String get lblAboutProvider => "حول مزود";

  @override
  String get lblNotRatedYet => "لم تقيم بعد";

  @override
  String get lblDeleteReview => "حذف التقييم";

  @override
  String get lblConfirmReviewSubTitle => 'هل تريد حذف هذه المراجعة؟';

  @override
  String get lblConfirmService => 'هل تريد الاحتفاظ بهذه الخدمة؟';

  @override
  String get lblConFirmResumeService => 'هل تريد استئناف هذه الخدمة؟';

  @override
  String get lblEndServicesMsg => "هل ترغب في إنهاء هذه الخدمة؟";

  @override
  String get lblCancelBooking => "إلغاء الحجز";

  @override
  String get lblStart => "بدء العمل";

  @override
  String get lblHold => "تعليق العمل";

  @override
  String get lblResume => "سيرة ذاتية";

  @override
  String get lblPayNow => "ادفع الآن";

  @override
  String get lblCheckStatus => "تحقق من حالة";

  @override
  String get lblID => "هوية شخصية";

  @override
  String get lblNoBookingsFound => "لا توجد حجوزات";

  @override
  String get lblCategory => "فئة";

  @override
  String get lblYourComment => "تعليقك";

  @override
  String get lblIntroducingCustomerRating => "تقديم تصنيف العملاء";

  @override
  String get lblSeeYourRatings => "انظر تقييماتك";

  @override
  String get lblFeatured => "مميزة";

  @override
  String get lblNoServicesFound => "لم يتم العثور على خدمات";

  @override
  String get lblGENERAL => "عام";

  @override
  String get lblAboutApp => "حول التطبيق";

  @override
  String get lblPurchaseCode => "شراء شفرة المصدر الكامل";

  @override
  String get lblNoRateYet => "حاليا لم تقيم أي خدمات";

  @override
  String get lblMemberSince => "عضو منذ ذلك الحين";

  @override
  String get lblFilterBy => "تصنيف بواسطة";

  @override
  String get lblClearFilter => "حذف التصنيف";

  @override
  String get lblNoReviews => "لم يتم تقديم تعليقات";

  @override
  String get lblUnreadNotification => "اشعار غير مقروء";

  @override
  String get lblChoosePaymentMethod => "اختر وسيلة الدفع";

  @override
  String get lblNoPayments => "لا مدفوعات";

  @override
  String get lblPayWith => "هل تريد أن تدفع مع";

  @override
  String get payWith => "ادفع من خلال";

  @override
  String get lblYourRating => "تقييمك";

  @override
  String get lblEnterReview => "أدخل رأيك (اختياري)";

  @override
  String get lblDelete => "حذف";

  @override
  String get lblDeleteRatingMsg => 'هل تريد حذف هذا التقييم؟';

  @override
  String get lblSelectRating => 'التصنيف مطلوب';

  @override
  String get lblNoServiceRatings => "لا تقييمات للخدمة";

  @override
  String get lblSearchFor => "بحث عن";

  @override
  String get lblRating => "تقييم";

  @override
  String get lblAvailableAt => "متاح في";

  @override
  String get lblRelatedServices => "الخدمات ذات الصلة";

  @override
  String get lblBookNow => "احجز الآن";

  @override
  String get lblWelcomeToHandyman => "مرحبا بكم في $APP_NAME.";

  @override
  String get lblWalkThroughSubTitle => "خدمة $APP_NAME - تطبيق خدمات المنزل عند الطلب مع حل كامل";

  @override
  String get textHandyman => 'بارع';

  @override
  String get lblChooseFromMap => "اختر من بين الخريطة";

  @override
  String get lblDeleteAddress => "حذف العنوان";

  @override
  String get lblDeleteSunTitle => 'هل تريد حذف هذا العنوان؟';

  @override
  String get lblFaq => "أسئلة وأجوبة";

  @override
  String get lblServiceFaq => "أسئلة وأجوبة الخدمة";

  @override
  String get lblLogoutTitle => "أوه لا ، أنت تغادر!";

  @override
  String get lblLogoutSubTitle => "هل ترغب بالخروج؟";

  @override
  String get lblFeaturedProduct => "هذا المنتج مميز";

  @override
  String get lblAlert => "إنذار";

  @override
  String get lblOnBase => "على أساس";

  @override
  String get lblInvalidCoupon => "كود القسيمة غير صالح";

  @override
  String get lblSelectCode => "يرجى اختيار رمز القسيمة";

  @override
  String get lblBackPressMsg => "اضغط مرة أخرى مرة أخرى للخروج من التطبيق";

  @override
  String get lblHour => "ساعة";

  @override
  String get lblHelplineNumber => "رقم خط المساعدة";

  @override
  String get lblSubcategories => "الفئات الفرعية";

  @override
  String get lblAgree => "أنا أوافق على";

  @override
  String get lblTermsOfService => "شروط الخدمة";

  @override
  String get lblWalkThrough0 => "$APP_NAME - $APP_NAME_TAG_LINE مع الحل الكامل ";

  @override
  String get lblServiceTotalTime => "إجمالي الخدمة";

  @override
  String get lblDateTimeUpdated => 'تم الانتهاء من تاريخ الحجز والوقت بنجاح';

  @override
  String get lblSelectDate => "الرجاء تحديد وقت التاريخ";

  @override
  String get lblReasonCancelling => "سبب:";

  @override
  String get lblReasonRejecting => "سبب رفض هذا الحجز";

  @override
  String get lblFailed => "سبب فشل هذا الحجز";

  @override
  String get lblNotDescription => "لا يوجد وصف متاح";

  @override
  String get lblMaterialTheme => "تمكين المواد التي تظهرها";

  @override
  String get lblServiceProof => "دليل الخدمة";

  @override
  String get lblAndroid12Support => "هذا الإجراء سيعيد تشغيل تطبيقك. يتأكد؟";

  @override
  String get lblOff => "تخفيض";

  @override
  String get lblHr => "ساعة";

  @override
  String get lblSignInWithGoogle => "الدخول مع جوجل";

  @override
  String get lblSignInWithOTP => 'تسجيل الدخول باستخدام OTP';

  @override
  String get lblDangerZone => "منطقة الخطر";

  @override
  String get lblDeleteAccount => "حذف الحساب";

  @override
  String get lblUnderMaintenance => "تحت الصيانة...";

  @override
  String get lblCatchUpAfterAWhile => "اللحاق بعد فترة من الوقت";

  @override
  String get lblId => "هوية شخصية";

  @override
  String get lblMethod => "الطريقة";

  @override
  String get lblStatus => "الحالة";

  @override
  String get lblPending => "قيد الانتظار";

  @override
  String get confirmationRequestTxt => 'هل تريد تنفيذ هذا الإجراء؟';

  @override
  String get lblDeleteAccountConformation => "سيتم حذف حسابك بشكل دائم. لن تتم استعادة بياناتك مرة أخرى.";

  @override
  String get lblAutoSliderStatus => "حالة التمرير التلقائي";

  @override
  String get lblPickAddress => "اختيار العنوان";

  @override
  String get lblUpdateDateAndTime => "تحديث التاريخ والوقت";

  @override
  String get lblRecheck => "إعادة فحص";

  @override
  String get lblLoginAgain => "الرجاد الدخول على الحساب من جديد";

  @override
  String get lblUpdate => "تحديث";

  @override
  String get lblNewUpdate => "تحديث جديد";

  @override
  String get lblOptionalUpdateNotify => "اشعار تحديث اختياري";

  @override
  String get lblAnUpdateTo => "تحديث ل";

  @override
  String get lblIsAvailableWouldYouLike => "متاح. هل ترغب في تحديث؟";

  @override
  String get lblRegisterAsPartner => "سجل كشريك";

  @override
  String get lblSignInWithApple => 'تسجيل الدخول مع أبل';

  @override
  String get lblWaitingForProviderApproval => "في انتظار موافقة المزود";

  @override
  String get lblFree => "غير مدفوع الأجر";

  @override
  String get lblAppleSignInNotAvailable => "تسجيل الدخول باستخدام Apple غير متوفرة لجهازك";

  @override
  String get lblTotalExtraCharges => "إجمالي الرسوم الإضافية";

  @override
  String get lblWaitingForResponse => "بإنتظار الرد";

  @override
  String get lblAll => "الجميع";

  @override
  String get noConversation => "لا محادثة";

  @override
  String get noConversationSubTitle => "لم تقم بأي محادثة بعد.";

  @override
  String get noBookingSubTitle => "يبدو أنك لم تحجز طلبك بعد";

  @override
  String get myReviews => "تقييماتي";

  @override
  String get noCategoryFound => "لم يتم العثور على فئة";

  @override
  String get noProviderFound => "لم يتم العثور على مزود";

  @override
  String get createServiceRequest => "إنشاء طلب خدمة";

  @override
  String get chooseImages => "اختر الصور";

  @override
  String get serviceDescription => "وصف الخدمة";

  @override
  String get addNewService => "أضف خدمة جديدة";

  @override
  String get newPostJobRequest => 'نشر طلب عمل جديد';

  @override
  String get postJobTitle => "اسم العمل";

  @override
  String get postJobDescription => " تفاصيل العمل";

  @override
  String get services => "خدمات";

  @override
  String get myPostJobList => "طلب الوظيفة المخصص لي";

  @override
  String get requestNewJob => "طلب وظيفة جديدة";

  @override
  String get noNotifications => "لا إشعارات";

  @override
  String get noNotificationsSubTitle => "سنقوم بإعلامك بمجرد أن يكون لدينا شيء لك";

  @override
  String get noFavouriteSubTitle => "ستظهر خدماتك المفضلة هنا";

  @override
  String get termsConditionsAccept => "يرجى قبول الشروط والأحكام";

  @override
  String get disclaimer => "تنصل";

  @override
  String get disclaimerContent => "سيُطلب منك الدفع بمجرد اكتمال الحجز.";

  @override
  String get inputMustBeNumberOrDigit => 'يجب أن يكون الإدخال رقمًا أو رمزا';

  @override
  String get requiredAfterCountryCode => 'مطلوب بعد رمز البلد';

  @override
  String get selectedOtherBookingTime => 'تم تمرير وقت الحجز المحدد بالفعل. الرجاء تحديد وقت آخر.';

  @override
  String get myServices => 'خدماتي';

  @override
  String get doYouWantToAssign => 'هل تريد تعيين';

  @override
  String get bidPrice => 'سعر العرض';

  @override
  String get accept => 'قبول';

  @override
  String get price => 'السعر';

  @override
  String get remove => 'إزالة';

  @override
  String get add => 'اضافة';

  @override
  String get save => 'حفظ';

  @override
  String get createPostJobWithoutSelectService => 'لا يمكنك نشر إنشاء وظيفة دون اختيار الخدمة';

  @override
  String get selectCategory => 'اختر الفئة';

  @override
  String get pleaseAddImage => 'الرجاء إضافة الصورة';

  @override
  String get selectedBookingTimeIsAlreadyPassed => 'تم تمرير وقت الحجز المحدد بالفعل. الرجاء تحديد وقت آخر.';

  @override
  String get jobPrice => 'سعر الوظيفة';

  @override
  String get estimatedPrice => 'السعر المقدر';

  @override
  String get bidder => 'مزايد';

  @override
  String get assignedProvider => 'مزود مخصص';

  @override
  String get myPostDetail => 'تفاصيل ما بعد ذلك';

  @override
  String get thankYou => 'شكرًا لك!';

  @override
  String get bookingConfirmedMsg => 'تم تأكيد حجزك.';

  @override
  String get goToHome => 'اذهب إلى الرئيسية';

  @override
  String get goToReview => 'اذهب للتقيميات';

  @override
  String get noServiceAdded => 'لم تتم إضافة خدمة';

  @override
  String get noPostJobFound => 'لم يتم العثور على وظيفة بعد';

  @override
  String get noPostJobFoundSubtitle => 'عندما تنشر وظيفتك ، سيتم إخطار كل مزود ، ويمكنك اختيار مزودك المطلوب لإنجاز المهمة.';

  @override
  String get pleaseEnterValidOTP => 'الرجاء إدخال كلمة المرور الصحيحة (OTP).';

  @override
  String get confirmOTP => 'تأكيد OTP';

  @override
  String get sendingOTP => 'إرسال OTP';

  @override
  String get pleaseSelectDifferentSlotThenPrevious => '""الرجاء تحديد اوقات عمل مختلفة ثم السابق';

  @override
  String get pleaseSelectTheSlotsFirst => 'الرجاء تحديد اوقات العمل أولاً';

  @override
  String get editTimeSlotsBooking => 'تحرير اوقات العمل  للحجز ';

  @override
  String get availableSlots => 'اوقات العمل المتاحة';

  @override
  String get noTimeSlots => 'لا اوقات عمل زمنية';

  @override
  String get bookingDateAndSlot => 'البحث عن التاريخ والفتحة';

  @override
  String get extraCharges => 'رسوم إضافية';

  @override
  String get chatCleared => 'تم مسح الدردشة';

  @override
  String get clearChat => 'دردشة واضحة';

  @override
  String get jobRequestSubtitle => 'خدمتك غير موجودة في النظام؟ لا تقلق ، يمكنك نشر متطلباتك.';

  @override
  String get verified => 'تم التحقق';

  @override
  String get theEnteredCodeIsInvalidPleaseTryAgain => 'الرمز الذي تم إدخاله غير صالح ، يرجى المحاولة مرة أخرى';

  @override
  String get otpCodeIsSentToYourMobileNumber => 'تم إرسال رمز OTP إلى رقم هاتفك المحمول';

  @override
  String get yourPaymentFailedPleaseTryAgain => 'فشلت دفعتك ، يرجى المحاولة مرة أخرى';

  @override
  String get yourPaymentHasBeenMadeSuccessfully => 'تم سداد دفعتك بنجاح';

  @override
  String get transactionFailed => 'فشل الاجراء';

  @override
  String get lblStep3 => "الخطوه 3";

  @override
  String get lblAvailableOnTheseDays => "متاح في هذه الأيام";

  @override
  String get internetNotAvailable => 'يبدو أن الإنترنت الخاص بك غير متصل';

  @override
  String get pleaseTryAgain => 'حاول مرة اخرى';

  @override
  String get somethingWentWrong => 'هناك خطأ ما';

  @override
  String get postJob => 'طلب عمل';

  @override
  String get package => 'حزمة';

  @override
  String get frequentlyBoughtTogether => 'اشترى في كثير من الأحيان جنبا إلى جنب';

  @override
  String get endOn => 'ينتهي';

  @override
  String get buy => 'شراء';

  @override
  String get includedServices => 'شملت الخدمات';

  @override
  String get includedInThisPackage => 'المدرجة في هذه الحزمة';

  @override
  String get lblInvalidTransaction => 'المعاملة غير صالحة';

  @override
  String get getTheseServiceWithThisPackage => 'ستحصل على هذه الخدمات مع هذه الحزمة';

  @override
  String get lblNotValidUser => 'مستخدم غير صحيح';

  @override
  String get lblSkip => 'تخطى';

  @override
  String get lblChangeCountry => 'تغيير الدولة';

  @override
  String get lblTimeSlotNotAvailable => 'اوقات العمل غير متوفرة';

  @override
  String get lblAdd => 'اضافة';

  @override
  String get lblThisService => 'هذه الخدمة';

  @override
  String get lblYourCurrenciesNotSupport => 'عملاتك لا تدعم CinetPay';

  @override
  String get lblSignInFailed => 'فشل تسجيل الدخول';

  @override
  String get lblUserCancelled => 'تم إلغاء المستخدم';

  @override
  String get lblTransactionCancelled => 'تم إلغاء المعاملة';

  @override
  String get lblExample => 'مثال';

  @override
  String get lblCheckOutWithCinetPay => 'الخروج مع CinetPay';

  @override
  String get lblLocationPermissionDenied => 'تم رفض أذونات استخدام الموقع.';

  @override
  String get lblLocationPermissionDeniedPermanently => 'يتم رفض أذونات الموقع بشكل دائم ، لا يمكننا طلب الأذونات.';

  @override
  String get lblEnableLocation => 'يرجى التأكد من تمكين خدمات الموقع.';

  @override
  String get lblNoUserFound => 'لم يتم العثور على المستخدم';

  @override
  String get lblUserNotCreated => 'لم يتم إنشاء المستخدم';

  @override
  String get lblTokenExpired => 'انتهت صلاحية الرمز';

  @override
  String get lblConfirmationForDeleteMsg => 'هل تريد حذف الرسالة؟';

  @override
  String get favouriteProvider => 'المزود المفضل';

  @override
  String get noProviderFoundMessage => 'سيظهر مقدمي الخدمات المفضلين لديك هنا';

  @override
  String get personalInfo => 'معلومات شخصية';

  @override
  String get essentialSkills => 'مهارات اساسيه';

  @override
  String get knownLanguages => 'اللغات المعروفة';

  @override
  String get authorBy => 'مؤلف';

  @override
  String get views => 'الآراء';

  @override
  String get blogs => 'المدونات';

  @override
  String get noBlogsFound => 'لم يتم العثور على مدونات';

  @override
  String get requestInvoice => 'طلب الفاتورة';

  @override
  String get invoiceSubTitle => 'أدخل عنوان البريد الإلكتروني حيث ترغب في تلقي فاتورتك';

  @override
  String get sentInvoiceText => 'يرجى التحقق من بريدك الإلكتروني الذي الرسلنا اليه الفاتورة.';

  @override
  String get send => 'ارسال';

  @override
  String get published => 'تم النشر';

  @override
  String get clearChatMessage => 'هل تريد مسح هذه الدردشة؟';

  @override
  String get deleteMessage => 'هل تريد أن تحذف';

  @override
  String get accepted => 'مقبول';

  @override
  String get onGoing => 'جاري التنفيذ';

  @override
  String get inProgress => 'قيد العمل';

  @override
  String get cancelled => 'ألغيت';

  @override
  String get rejected => 'مرفوض';

  @override
  String get failed => 'فشل';

  @override
  String get completed => 'مكتمل';

  @override
  String get pendingApproval => 'ما زال يحتاج الموافقة';

  @override
  String get waiting => 'انتظار';

  @override
  String get paid => 'مدفوع';

  @override
  String get advancePaid => 'دفع مسبقا';

  @override
  String get insufficientBalanceMessage => 'لا يوجد رصيد كافي في محفظتك. الرجاء اختيار طريقة أخرى.';

  @override
  String get cinetPayNotSupportedMessage => 'CinetPay غير مدعوم من عملاتك';

  @override
  String get loading => 'تحميل..';

  @override
  String get walletBalance => 'رصيد المحفظة';

  @override
  String get payAdvance => 'دفع تقدم';

  @override
  String get advancePaymentMessage => 'قم بعمل دفعة مسبقة لإكمال الحجز';

  @override
  String get advancePayAmount => 'مبلغ الدفع المسبق';

  @override
  String get remainingAmount => 'الكمية المتبقية';

  @override
  String get advancePayment => 'الدفع المسبق';

  @override
  String get withExtraAndAdvanceCharge => 'مع رسوم إضافية و الدفع المسبق';

  @override
  String get withExtraCharge => 'مع رسوم إضافية';

  @override
  String get min => 'دقيقة';

  @override
  String get hour => 'ساعة';

  @override
  String get customerRatingMessage => 'أخبر الآخرين برأيك';

  @override
  String get paymentHistory => 'سجل الدفع';

  @override
  String get message => 'رسالة';

  @override
  String get wallet => 'محفظة';

  @override
  String get payWithFlutterWave => 'دفع مع Flutterwave';

  @override
  String get goodMorning => 'صباح الخير';

  @override
  String get goodAfternoon => 'مساء الخير';

  @override
  String get goodEvening => 'مساء الخير';

  @override
  String get invalidURL => 'URL غير صالح';

  @override
  String get use24HourFormat => 'استخدام تنسيق 24 ساعة؟';

  @override
  String get email => 'بريد إلكتروني';

  @override
  String get badRequest => 'طلب سىء';

  @override
  String get forbidden => 'ممنوع';

  @override
  String get pageNotFound => 'الصفحة لم يتم العثور عليها';

  @override
  String get tooManyRequests => 'الكثير من الطلبات';

  @override
  String get internalServerError => 'خطأ الخادم الداخلي';

  @override
  String get badGateway => 'مدخل غير صالح';

  @override
  String get serviceUnavailable => 'الخدمة غير متوفرة';

  @override
  String get gatewayTimeout => 'البوابة انتهى الزمن';

  @override
  String get pleaseWait => 'انتظر من فضلك';

  @override
  String get externalWallet => 'محفظة خارجية';

  @override
  String get userNotFound => 'لم يتم العثور على المستخدم';

  @override
  String get requested => 'مطلوب';

  @override
  String get assigned => 'مُكَلَّف';

  @override
  String get reload => 'إعادة تحميل';

  @override
  String get lblStripeTestCredential => 'لا يمكن اختبار بيانات الاعتماد أكثر من 500';

  @override
  String get noDataFoundInFilter => 'اختر أفضل معايير البحث للحصول على أفضل النتائج';

  @override
  String get addYourCountryCode => 'أضف رمز بلدك';

  @override
  String get help => 'مساعدة';

  @override
  String get couponCantApplied => 'لا يمكن تطبيق هذه القسيمة';

  @override
  String get priceAmountValidationMessage => 'يجب أن يكون مبلغ السعر مبشرة من 0';

  @override
  String get pleaseWaitWhileWeLoadChatDetails => 'يرجى الانتظار أثناء تحميل تفاصيل الدردشة';

  @override
  String get isNotAvailableForChat => 'غير متوفر للدردشة';

  @override
  String get connectWithFirebaseForChat => 'تواصل مع Firebase للدردشة';

  @override
  String get closeApp => 'أغلق التطبيق';

  @override
  String get providerAddedToFavourite => 'الموفر إضافة إلى القائمة المفضلة';

  @override
  String get providerRemovedFromFavourite => 'تم إزالته من القائمة المفضلة';

  @override
  String get provideValidCurrentPasswordMessage => 'يجب عليك توفير كلمة مرور الحالية صالحة';

  @override
  String get copied => 'نسخ';

  @override
  String get copyMessage => 'نسخ الرسالة';

  @override
  String get messageDelete => 'حذف رسالة';

  @override
  String get pleaseChooseAnyOnePayment => 'الرجاء اختيار أي طريقة دفع واحدة';

  @override
  String get myWallet => 'محفظتى';

  @override
  String get balance => 'توازن';

  @override
  String get topUpWallet => 'محفظة أعلى';

  @override
  String get topUpAmountQuestion => 'ما هو المبلغ الذي تفضل أن تتفوق عليه؟';

  @override
  String get paymentMethod => 'طريقة الدفع او السداد';

  @override
  String get selectYourPaymentMethodToAddBalance => 'حدد طريقة الدفع الخاصة بك لإضافة رصيد';

  @override
  String get proceedToTopUp => 'انتقل إلى أعلى';

  @override
  String get serviceAddedToFavourite => 'تمت إضافة الخدمة إلى القائمة المفضلة';

  @override
  String get serviceRemovedFromFavourite => 'تمت إزالة الخدمة من القائمة المفضلة';

  @override
  String get firebaseRemoteCannotBe => 'لا يمكن توصيل جهاز التحكم عن بعد الخاص بـ Firebase';

  @override
  String get search => 'يبحث';

  @override
  String get close => 'يغلق';

  @override
  String get totalAmountShouldBeMoreThan => 'يجب أن يكون المبلغ الإجمالي أكثر من';

  @override
  String get totalAmountShouldBeLessThan => 'يجب أن يكون المبلغ الإجمالي أقل من';

  @override
  String get doYouWantToTopUpYourWallet => 'هل تريد أن تسبق محفظتك الآن؟';

  @override
  String get chooseYourLocation => 'اختر موقعك';

  @override
  String get connect => 'يتصل';

  @override
  String get transactionId => 'رقم المعاملة';

  @override
  String get at => 'في';

  @override
  String get appliedTaxes => 'الضرائب التطبيقية';

  @override
  String get accessDeniedContactYourAdmin => 'تم الرفض. اتصل بمسؤولك للحصول على المساعدة.';

  @override
  String get yourWalletIsUpdated => 'تم تحديث محفظتك!';

  @override
  String get by => 'بواسطة';

  @override
  String get noPaymentMethodFound => 'لم يتم العثور على طريقة دفع';

  @override
  String get theAmountShouldBeEntered => 'يجب إدخال المبلغ';

  @override
  String get walletHistory => 'تاريخ المحفظة';

  @override
  String get debit => 'دَين';

  @override
  String get credit => 'ائتمان';

  @override
  String get youCannotApplyThisCoupon => 'لا يمكنك تطبيق هذه القسيمة';

  @override
  String get basedOn => 'مرتكز على';

  @override
  String get serviceStatusPicMessage => 'يرجى التأكد من اختيار حالة حجز واحدة على الأقل';

  @override
  String get clearFilter => 'مرشح واضح';

  @override
  String get bookingStatus => 'وضع الحجز';

  @override
  String get addOns => 'الوظائف الإضافية';

  @override
  String get serviceAddOns => 'الوظائف الإضافية للخدمة';

  @override
  String get turnOn => 'شغله';

  @override
  String get turnOff => 'أطفأ';

  @override
  String get serviceVisitType => 'نوع زيارة الخدمة';

  @override
  String get thisServiceIsOnlineRemote => 'سيتم إكمال هذه الخدمة عبر الإنترنت/عن بُعد.';

  @override
  String get deleteMessageForAddOnService => 'هل تريد إزالة هذه الخدمة الإضافية؟';

  @override
  String get confirmation => 'تأكيد!';

  @override
  String get pleaseNoteThatAllServiceMarkedCompleted => 'يرجى ملاحظة أن جميع الوظائف الإضافية للخدمة محددة كما اكتملت!';

  @override
  String get writeHere => 'اكتب هنا';

  @override
  String get isAvailableGoTo => 'متاح. انتقل إلى المتجر وتنزيل الإصدار الجديد من التطبيق.';

  @override
  String get later => 'لاحقاً';

  @override
  String get whyChooseMe => 'لماذا تختارني؟';

  @override
  String get useThisCodeToGet => 'استخدم هذا الرمز للحصول على';

  @override
  String get off => 'عن';

  @override
  String get applied => 'مُطبَّق';

  @override
  String get coupons => 'كوبونات';

  @override
  String get handymanList => 'قائمة الماشية';

  @override
  String get noHandymanFound => 'لم يتم العثور على الماشية';

  @override
  String get back => 'خلف';

  @override
  String get team => 'فريق';

  @override
  String get whyChooseMeAs => 'لماذا تختارني كمزود خدمة موثوق به';

  @override
  String get reason => 'سبب';

  @override
  String get pleaseEnterAddressAnd => 'الرجاء إدخال العنوان وحجز تاريخ وفتحة';

  @override
  String get pleaseEnterYourAddress => 'الرجاء إدخال عنوانك';

  @override
  String get pleaseSelectBookingDate => 'الرجاء تحديد تاريخ الحجز والفتحة';

  @override
  String get doYouWantTo => 'هل تريد إزالة هذه القسيمة؟';

  @override
  String get chooseDateTime => 'اختر التاريخ والوقت';

  @override
  String get airtelMoneyPayment => 'Airtel الدفع المال';

  @override
  String get recommendedForYou => 'موصى به لك';

  @override
  String get paymentSuccess => 'الدفع الناجح';

  @override
  String get redirectingToBookings => 'إعادة توجيه الحجوزات ..';

  @override
  String get transactionIsInProcess => 'المعاملة قيد التشغيل ...';

  @override
  String get pleaseCheckThePayment => 'يرجى التحقق من طلب الدفع إلى رقمك';

  @override
  String get enterYourMsisdnHere => 'أدخل msisdn الخاص بك هنا';

  @override
  String get theTransactionIsStill => 'لا تزال المعاملة معالجة وهي في حالة غامضة. يرجى إجراء استفسار المعاملة لجلب حالة المعاملة.';

  @override
  String get transactionIsSuccessful => 'عملية ناجحة';

  @override
  String get incorrectPinHasBeen => 'تم إدخال رقم التعريف الشخصي غير الصحيح';

  @override
  String get theUserHasExceeded => 'لقد تجاوز المستخدم حد معاملة محفظته المسموح به';

  @override
  String get theAmountUserIs => 'المبلغ الذي يحاول المستخدم نقله أقل من الحد الأدنى المسموح به';

  @override
  String get userDidnTEnterThePin => 'لم يدخل المستخدم الرقم التعريف الشخصي';

  @override
  String get transactionInPendingState => 'المعاملة في حالة معلقة. يرجى التحقق بعد وقت ما';

  @override
  String get userWalletDoesNot => 'لا تحتوي محفظة المستخدم على ما يكفي من المال لتغطية المبلغ المستحق';

  @override
  String get theTransactionWasRefused => 'تم رفض الصفقة';

  @override
  String get thisIsAGeneric => 'هذا رفض عام له عدة أسباب محتملة';

  @override
  String get payeeIsAlreadyInitiated => 'تم بالفعل بدء Pleadee لـ Churn أو Barred أو غير مسجل على منصة Airtel Money';

  @override
  String get theTransactionWasTimed => 'تم توقيت المعاملة.';

  @override
  String get theTransactionWasNot => 'لم يتم العثور على المعاملة.';

  @override
  String get xSignatureAndPayloadDid => 'لا يتطابق التوقيع X والحمولة';

  @override
  String get encryptionKeyHasBeen => 'تم جلب مفتاح التشفير بنجاح';

  @override
  String get couldNotFetchEncryption => 'لا يمكن جلب مفتاح التشفير';

  @override
  String get transactionHasBeenExpired => 'انتهت صلاحية المعاملة';

  @override
  String get ambiguous => 'غامض';

  @override
  String get success => 'نجاح';

  @override
  String get incorrectPin => 'دبوس غير صحيح';

  @override
  String get exceedsWithdrawalAmountLimitS => 'يتجاوز حد مبلغ السحب (ق)';

  @override
  String get invalidAmount => 'مبلغ غير صحيح';

  @override
  String get transactionIdIsInvalid => 'معرف المعاملة غير صالح';

  @override
  String get inProcess => 'تحت المعالجة';

  @override
  String get notEnoughBalance => 'لا يكفي التوازن';

  @override
  String get refused => 'رفض';

  @override
  String get doNotHonor => 'لا تتباهي';

  @override
  String get transactionNotPermittedTo => 'المعاملة غير مسموح بالوظف';

  @override
  String get transactionTimedOut => 'المعاملة توقيت خارج';

  @override
  String get transactionNotFound => 'المعاملة غير موجودة';

  @override
  String get forBidden => 'مُحرَّم';

  @override
  String get successfullyFetchedEncryptionKey => 'مفتاح التشفير بنجاح';

  @override
  String get errorWhileFetchingEncryption => 'خطأ أثناء جلب مفتاح التشفير';

  @override
  String get transactionExpired => 'انتهت صلاحية المعاملة';

  @override
  String get verifyEmail => 'التحقق من البريد الإلكتروني';

  @override
  String get minRead => 'اقرأ مين';

  @override
  String get loadingChats => 'تحميل الدردشات ...';

  @override
  String get monthly => 'شهريا';

  @override
  String get noCouponsAvailableMsg => 'لا كوبونات في الوقت الحالي. استمر في التحقق من العروض الحصرية!';

  @override
  String get refundPolicy => 'سياسة الاسترجاع';

  @override
  String get chooseAnyOnePayment => 'اختر أي طريقة دفع واحدة أولاً';

  @override
  String get january => 'يناير';

  @override
  String get february => 'شهر فبراير';

  @override
  String get march => 'يمشي';

  @override
  String get april => 'أبريل';

  @override
  String get may => 'يمكن';

  @override
  String get june => 'يونيو';

  @override
  String get july => 'يوليو';

  @override
  String get august => 'أغسطس';

  @override
  String get september => 'سبتمبر';

  @override
  String get october => 'اكتوبر';

  @override
  String get november => 'شهر نوفمبر';

  @override
  String get december => 'ديسمبر';

  @override
  String get monthName => 'اسم الشهر';

  @override
  String get mon => 'الاثنين';

  @override
  String get tue => 'الثلاثاء';

  @override
  String get wed => 'تزوج';

  @override
  String get thu => 'الخميس';

  @override
  String get fri => 'الجمعة';

  @override
  String get sat => 'قعد';

  @override
  String get sun => 'شمس';

  @override
  String get weekName => 'اسم الأسبوع';

  @override
  String get removeThisFile => 'إزالة هذا الملف';

  @override
  String get areYouSureWantToRemoveThisFile => 'هل تريد إزالة هذا الملف؟';

  @override
  String get sendMessage => 'أرسل رسالة';

  @override
  String get youAreNotConnectedWithChatServer => 'الاتصال بخادم الدردشة';

  @override
  String get NotConnectedWithChatServerMessage => 'أنت غير متصل بخادم الدردشة. اضغط على الزر أدناه للاتصال وبدء الدردشة';

  @override
  String get sentYouAMessage => 'أرسلت لك رسالة';

  @override
  String get pushNotification => 'دفع الإخطار';

  @override
  String get yourBooking => 'حجزك';

  @override
  String get featuredServices => 'خدمات مميزة';

  @override
  String get postYourRequestAnd => 'انشر طلبك، وسنبذل قصارى جهدنا لتحقيقه';

  @override
  String get newRequest => 'طلب جديد';

  @override
  String get upcomingBooking => 'الحجز القادم';

  @override
  String get theUserHasDenied => 'رفض المستخدم استخدام التعرف على الكلام';

  @override
  String get helloGuest => 'مرحباً بالضيف';

  @override
  String get eGCleaningPlumberPest => 'على سبيل المثال تنظيف، سباك، مكافحة الحشرات';

  @override
  String get ifYouDidnTFind => 'إذا لم تجد خدمتنا، فلا تقلق! يمكنك بسهولة نشر طلبك.';

  @override
  String get popularServices => 'الخدمات الشعبية';

  @override
  String get canTFindYourServices => 'لا تستطيع العثور على خدماتك؟';

  @override
  String get trackProviderLocation => 'تتبع موقع المزود';

  @override
  String get trackHandymanLocation => 'تتبع موقع العامل الماهر';

  @override
  String get handymanLocation => 'موقع العامل الماهر';

  @override
  String get providerLocation => 'موقع المزود';

  @override
  String get lastUpdatedAt => 'آخر تحديث في:';

  @override
  String get track => 'مسار';

  @override
  String get handymanReached => 'هل وصل العامل الماهر؟ انقر لتبدأ';

  @override
  String get providerReached => 'هل وصل المزود؟ انقر لتبدأ';

  @override
  String get addBank => "أضف البنك";

  @override
  String get bankList => "قائمة البنك";

  @override
  String get lbldefault => "تقصير";

  @override
  String get setAsDefault => "تعيين كافتراضي";

  @override
  String get aadharNumber => "رقم أدهار";

  @override
  String get panNumber => "رقم الحساب الجاري";

  @override
  String get lblPleaseEnterAccountNumber => "الرجاء إدخال رقم الحساب";

  @override
  String get lblAccountNumberMustContainOnlyDigits => "يجب أن يحتوي رقم الحساب على أرقام فقط";

  @override
  String get lblAccountNumberMustBetween11And16Digits => "يجب أن يتراوح رقم الحساب بين 11 و16 رقمًا";

  @override
  String get noBankDataTitle => "لم يتم العثور على بيانات البنك";

  @override
  String get noBankDataSubTitle => "لم تقم بإضافة البنك بعد";

  @override
  String get lblBankDetails => "التفاصيل المصرفية";

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get deleteBankTitle => 'هل تريد حذف هذا البنك؟';

  @override
  String get bankName => 'اسم البنك';

  @override
  String get accountNumber => 'رقم حساب';

  @override
  String get iFSCCode => 'رمز IFSC';

  @override
  String get lblEdit => 'تحرير';

  @override
  String get availableBalance => "الرصيد المتوفر";

  @override
  String get withdraw => "ينسحب";

  @override
  String get chooseBank => "اختر البنك";

  @override
  String get egCentralNationalBank => 'على سبيل المثال "البنك الوطني المركزي"';

  @override
  String get successful => 'ناجح';

  @override
  String get yourWithdrawalRequestHasBeenSuccessfullySubmitted => 'لقد تم إرسال طلب السحب الخاص بك بنجاح.';

  @override
  String get eg3000 => 'على سبيل المثال "3000"';

  @override
  String get withdrawRequest => 'تم إرسال طلب السحب بنجاح!';

  @override
  String get lblEnterAmount => "أدخل المبلغ";

  @override
  String get pleaseAddLessThanOrEqualTo => "الرجاء إضافة أقل من أو يساوي";

  @override
  String get topUp => "فوق حتى";

  @override
  String get btnSave => 'حفظ';

  @override
  String get fullNameOnBankAccount => 'الاسم الكامل على الحساب البنكي';

  @override
  String get packageIsExpired => 'انتهت صلاحية الحزمة';

  @override
  String get bookPackage => 'حزمة الكتاب';

  @override
  String get packageDescription => 'وصف الحزمة';

  @override
  String get packagePrice => 'سعر الحزمة';

  @override
  String get online => 'متصل';

  @override
  String get noteAddressIsNot => 'ملاحظة: العنوان غير مطلوب للخدمات البعيدة.';

  @override
  String get wouldYouLikeTo => 'هل ترغب في المتابعة وتأكيد هذا الحجز؟';

  @override
  String get packageName => 'اسم الحزمة';

  @override
  String get feeAppliesForCancellations => 'تنطبق الرسوم على الإلغاءات التي تتم داخل';

  @override
  String get a => 'أ';

  @override
  String get byConfirmingYouAgree => 'من خلال التأكيد، فإنك توافق على لدينا';

  @override
  String get and => 'و';

  @override
  String get areYouSureYou => 'هل أنت متأكد أنك تريد الإلغاء؟ قد يتم تطبيق رسوم الإلغاء على أساس سعر الخدمة الخاصة بك';

  @override
  String get totalCancellationFee => 'إجمالي رسوم الإلغاء';

  @override
  String get goBack => 'عُد';

  @override
  String get bookingCancelled => 'تم إلغاء الحجز';

  @override
  String get yourBookingHasBeen => 'لقد تم إلغاء حجزك بنجاح. ستتم معالجة استرداد الأموال المطبق خلال 24 ساعة';

  @override
  String get noteCheckYourBooking => 'ملحوظة: تحقق من سجل الحجز الخاص بك للحصول على تفاصيل استرداد الأموال';

  @override
  String get cancelledReason => 'سبب الإلغاء';

  @override
  String get refundPaymentDetails => 'تفاصيل دفع الاسترداد';

  @override
  String get refundOf => 'استرداد';

  @override
  String get refundAmount => 'مبلغ الاسترداد';

  @override
  String get cancellationFee => 'رسوم الإلغاء';

  @override
  String get advancedPayment => 'الدفع المتقدم';

  @override
  String get hoursOfTheScheduled => 'ساعات الخدمة المجدولة';

  @override
  String get open => 'يفتح';

  @override
  String get closed => 'مغلق';

  @override
  String get createBy => 'إنشاء بواسطة';

  @override
  String get repliedBy => 'تم الرد بواسطة';

  @override
  String get closedBy => 'مغلق بواسطة';

  @override
  String get helpDesk => 'مكتب المساعدة';

  @override
  String get addNew => 'أضف جديد';

  @override
  String get queryYet => 'الاستعلام بعد';

  @override
  String get toSubmitYourProblems => 'لإرسال مشاكلك، ما عليك سوى الضغط على زر ""إضافة"" وشرح مخاوفك';

  @override
  String get noRecordsFoundFor => 'لم يتم العثور على سجلات ل';

  @override
  String get queries => 'الاستعلامات.';

  @override
  String get noActivityYet => 'لا يوجد نشاط حتى الآن';

  @override
  String get noRecordsFound => 'لم يتم العثور على سجلات';

  @override
  String get reply => 'رد';

  @override
  String get eGDuringTheService => 'على سبيل المثال أثناء الخدمة، تعرض الأثاث للتلف عن طريق الخطأ.';

  @override
  String get doYouWantClosedThisQuery => 'هل تريد إغلاق هذا الاستعلام';

  @override
  String get markAsClosed => 'وضع علامة كمغلق';

  @override
  String get youCanMarkThis => 'يمكنك وضع علامة ""مغلق"" على هذا إذا كنت راضيًا عن إجابتنا';

  @override
  String get subject => 'موضوع';

  @override
  String get eGDamagedFurniture => 'على سبيل المثال الأثاث التالف';

  @override
  String get closedOn => 'مغلق في:';

  @override
  String get on => 'على';

  @override
  String get showMessage => 'إظهار الرسالة';

  @override
  String get yesterday => 'أمس';

  @override
  String get chooseAction => 'اختر الإجراء';

  @override
  String get chooseImage => 'اختر الصورة';

  @override
  String get noteYouCanUpload => 'ملاحظة: يمكنك تحميل الصورة بامتدادات \'jpg\'، \'png\'، \'jpeg\' ويمكنك تحديد صورة واحدة فقط';

  @override
  String get removeImage => 'إزالة الصورة';

  @override
  String get advancedRefund => 'استرداد متقدم';

  @override
  String get lblService => 'خدمة';

  @override
  String get dateRange => 'النطاق الزمني';

  @override
  String get paymentType => 'نوع الدفع';

  @override
  String get reset => 'إعادة ضبط';

  @override
  String get noStatusFound => 'لم يتم العثور على حالة';

  @override
  String get selectStartDateEndDate => 'حدد تاريخ البدء وتاريخ الانتهاء';

  @override
  String get handymanNotFound => 'لم يتم العثور على العامل الماهر';

  @override
  String get providerNotFound => 'لم يتم العثور على الموفر';

  @override
  String get rateYourExperience => 'قيم تجربتك';

  @override
  String get weValueYourFeedback => 'نحن نقدر ملاحظاتك! يرجى تقييم تجربتك الأخيرة مع خدمتنا';

  @override
  String get viewStatus => 'عرض الحالة';

  @override
  String get paymentInfo => 'معلومات الدفع';

  @override
  String get mobile => 'متحرك:';

  @override
  String get to => 'ل';

  @override
  String get chooseYourDateRange => 'اختر النطاق الزمني الخاص بك';

  @override
  String get asHandyman => 'كعامل ماهر';

  @override
  String get passwordLengthShouldBe => 'يجب أن يكون طول كلمة المرور من 8 إلى 12 حرفًا.';

  @override
  String get cash => 'نقدي';

  @override
  String get bank => 'بنك';


  @override
  String get razorPay => "رازور باي";

  @override
  String get payPal => "باي بال";

  @override
  String get stripe => "سترايب";

  @override
  String get payStack => "باي ستاك";

  @override
  String get flutterWave => "فلوتر ويف";

  @override
  String get paytm => "باي تي إم";

  @override
  String get airtelMoney => "ايرتل موني";

  @override
  String get cinet => "سينيت";

  @override
  String get midtrans => "ميدترانس";

  @override
  String get sadadPayment => "سداد";

  @override
  String get phonePe => "فون بي";

  @override
  String get inAppPurchase => "شراء داخل التطبيق";

  @override
  String get pix => "بيكس";

  @override
  String get chooseWithdrawalMethod => "اختر طريقة السحب";
}