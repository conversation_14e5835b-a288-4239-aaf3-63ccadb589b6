import '../utils/configs.dart';
import 'languages.dart';

class LanguageFr extends BaseLanguage {
  @override
  String get walkTitle1 => 'Créez et configurez votre compte';

  @override
  String get walkTitle2 => 'Parcourir et réserver des services';

  @override
  String get walkTitle3 => 'Suivez et gérez vos réservations';

  @override
  String get getStarted => "Commencer";

  @override
  String get signIn => "S'identifier";

  @override
  String get signUp => "S'inscrire";

  @override
  String get hintFirstNameTxt => "Entrez votre prénom";

  @override
  String get hintLastNameTxt => "Entrez votre nom de famille";

  @override
  String get hintContactNumberTxt => "Entrez votre numéro de contact";

  @override
  String get hintEmailAddressTxt => "Entrez votre adresse email";

  @override
  String get hintUserNameTxt => "Nom d'utilisateur";

  @override
  String get hintPasswordTxt => "Tapez votre mot de passe";

  @override
  String get hintReenterPasswordTxt => "Entrez à nouveau votre mot de passe";

  @override
  String get confirm => "Confirmer";

  @override
  String get hintEmailTxt => "Entrer votre Email";

  @override
  String get forgotPassword => "Mot de passe oublié?";

  @override
  String get alreadyHaveAccountTxt => "Vous avez déjà un compte?";

  @override
  String get rememberMe => "Souviens-toi de moi";

  @override
  String get resetPassword => "réinitialiser le mot de passe";

  @override
  String get dashboard => "Tableau de bord";

  @override
  String get editProfile => "Editer le profil";

  @override
  String get camera => "Caméra";

  @override
  String get language => "Langue";

  @override
  String get appTheme => "Thème de l'application";

  @override
  String get bookingHistory => "Historique des réservations";

  @override
  String get rateUs => "Évaluez nous";

  @override
  String get termsCondition => "Termes et conditions";

  @override
  String get helpSupport => "Support d'aide";

  @override
  String get privacyPolicy => "Politique de confidentialité";

  @override
  String get about => "Sur";

  @override
  String get logout => "Se déconnecter";

  @override
  String get chooseTheme => "Choisissez le thème de l'application";

  @override
  String get selectCountry => "Choisissez le pays";

  @override
  String get selectState => "Sélectionnez l'état";

  @override
  String get selectCity => "Sélectionnez une ville";

  @override
  String get changePassword => "Changer le mot de passe";

  @override
  String get passwordNotMatch => "Le mot de passe ne correspond pas";

  @override
  String get doNotHaveAccount => "Vous n'avez pas de compte?";

  @override
  String get hintNewPasswordTxt => "Entrez votre nouveau mot de passe";

  @override
  String get hintOldPasswordTxt => "Entrez votre ancien mot de passe";

  @override
  String get hintAddress => "Entrez votre adresse";

  @override
  String get lblGallery => "Galerie";

  @override
  String get yourReview => "Votre avis";

  @override
  String get review => "Commentaires";

  @override
  String get hintDescription => "Entrez votre description";

  @override
  String get lblApply => "Appliquer";

  @override
  String get bookTheService => "Réservez le service";

  @override
  String get contactAdmin => "S'il vous plaît contacter avec admin";

  @override
  String get allServices => "Tous les services";

  @override
  String get duration => "Durée";

  @override
  String get hourly => "horaire";

  @override
  String get payment => "Paiement";

  @override
  String get done => "Terminé";

  @override
  String get totalAmount => "Montant total";

  @override
  String get applyCoupon => "Appliquer Coupon";

  @override
  String get priceDetail => "Détail de prix";

  @override
  String get home => "Accueil";

  @override
  String get category => "Catégories";

  @override
  String get booking => "Réservation";

  @override
  String get profile => "Profil";

  @override
  String get lblAlertBooking => 'Voulez-vous réserver le service?';

  @override
  String get serviceName => "Nom du service";

  @override
  String get service => "Prestations de service";

  @override
  String get lblCancelReason => "Veuillez entrer la raison pour l'annulation de cette réservation.";

  @override
  String get enterReason => "Spécifiez la raison ici";

  @override
  String get noDataAvailable => "Pas de données disponibles";

  @override
  String get lblOk => "D'accord";

  @override
  String get paymentDetail => "Détail du paiement";

  @override
  String get paymentStatus => "Statut de paiement";

  @override
  String get viewDetail => "Voir les détails";

  @override
  String get appThemeLight => "Léger";

  @override
  String get appThemeDark => "Sombre";

  @override
  String get appThemeDefault => "Défaillance du système";

  @override
  String get markAsRead => "Tout marquer comme lu";

  @override
  String get lblYes => "Oui";

  @override
  String get lblNo => "Non";

  @override
  String get btnRate => "Noter maintenant";

  @override
  String get btnSubmit => "Soumettre";

  @override
  String get walkThrough1 => 'Inscrivez-vous ou connectez-vous à votre compte en utilisant votre courrier électronique ou vos profils de réseaux sociaux. Remplir votre profil garantit une expérience de réservation fluide.';

  @override
  String get walkThrough2 =>
      'Découvrez une large gamme de services disponibles dans votre région. Sélectionnez un service, choisissez un créneau horaire pratique et fournissez les détails de votre localisation pour réserver un service rapidement et facilement.';

  @override
  String get walkThrough3 => "Gardez une trace de l'état de votre service en temps réel. Consultez et gérez vos réservations actuelles et passées. Reprogrammez ou annulez les services à venir sans effort.";

  @override
  String get lblNotification => "Notifications";

  @override
  String get lblUnAuthorized => "L'utilisateur de démonstration ne peut être accordé pour cette action";

  @override
  String get btnNext => "Prochain";

  @override
  String get lblViewAll => "Voir tout";

  @override
  String get notAvailable => "pas disponible";

  @override
  String get lblFavorite => "Services préférés";

  @override
  String get lblChat => "Discuter";

  @override
  String get getLocation => "Obtenir l'emplacement";

  @override
  String get setAddress => "Régler l'adresse";

  @override
  String get requiredText => "Ce champ est obligatoire";

  @override
  String get phnRequiredText => "S'il vous plaît entrer le numéro de mobile";

  @override
  String get lblCall => "Appel";

  @override
  String get lblRateHandyman => "Taux Handyman";

  @override
  String get msgForLocationOn => 'Votre emplacement est allumé. Continuez à consulter les services disponibles dans tous les domaines?';

  @override
  String get msgForLocationOff => 'Votre emplacement est éteint. Découvrez et trouvez des services à la disposition de votre zone sélectionnée.';

  @override
  String get lblEnterPhnNumber => "Entrez votre numéro de téléphone";

  @override
  String get btnSendOtp => "Envoyer OTP";

  @override
  String get lblLocationOff => "Tous les services disponibles";

  @override
  String get lblAppSetting => "Réglage de l'application";

  @override
  String get lblSubTotal => "Montant de la taxe";

  @override
  String get lblImage => "Image";

  @override
  String get lblVideo => "Vidéo";

  @override
  String get lblAudio => "l'audio";

  @override
  String get lblChangePwdTitle => "Votre nouveau mot de passe doit être différent du mot de passe précédent utilisé";

  @override
  String get lblForgotPwdSubtitle => "Un maillon de mot de passe de réinitialisation sera envoyé à l'adresse e-mail saisie ci-dessus.";

  @override
  String get lblLoginTitle => "Rebonjour";

  @override
  String get lblLoginSubTitle => "Bienvenue, vous êtes manqué depuis longtemps";

  @override
  String get lblOrContinueWith => "Ou continuer avec";

  @override
  String get lblHelloUser => "Bonjour utilisateur!";

  @override
  String get lblSignUpSubTitle => 'Créez votre compte pour une meilleure expérience';

  @override
  String get lblStepper1Title => "Entrer des informations détaillées";

  @override
  String get lblDateAndTime => "Date et l'heure:";

  @override
  String get chooseDateAndTime => "Choisissez la date et l'heure";

  @override
  String get lblYourAddress => "Votre adresse";

  @override
  String get lblEnterYourAddress => "Entrez votre adresse";

  @override
  String get lblUseCurrentLocation => "Utiliser l'emplacement actuel";

  @override
  String get lblEnterDescription => "Entrez description";

  @override
  String get lblPrice => "Prix";

  @override
  String get lblTax => "Impôt";

  @override
  String get lblDiscount => "Rabais";

  @override
  String get lblAvailableCoupons => "Coupons disponibles";

  @override
  String get lblPrevious => "Précédent";

  @override
  String get lblCoupon => "Coupon";

  @override
  String get lblEditYourReview => "Modifier votre avis";

  @override
  String get lblTime => "Temps";

  @override
  String get textProvider => "Fournisseur";

  @override
  String get lblConfirmBooking => "Confirmer la réservation";

  @override
  String get lblConfirmMsg => "Êtes-vous sûr de vouloir confirmer cette réservation?";

  @override
  String get lblCancel => "Annuler";

  @override
  String get lblExpiryDate => "Date d'expiration :";

  @override
  String get lblRemoveCoupon => "Supprimer le coupon";

  @override
  String get lblNoCouponsAvailable => "Aucun coupon disponible";

  @override
  String get lblStep1 => "Étape 1";

  @override
  String get lblStep2 => "Étape 2";

  @override
  String get lblBookingID => "ID de réservation";

  @override
  String get lblDate => "Date";

  @override
  String get lblAboutHandyman => "À propos de Handyman";

  @override
  String get lblAboutProvider => "À propos du fournisseur";

  @override
  String get lblNotRatedYet => "Vous n'avez pas encore été évalué";

  @override
  String get lblDeleteReview => "Supprimer l'examen";

  @override
  String get lblConfirmReviewSubTitle => 'Voulez-vous supprimer cet avis?';

  @override
  String get lblConfirmService => 'Voulez-vous conserver ce service?';

  @override
  String get lblConFirmResumeService => 'Souhaitez-vous reprendre ce service?';

  @override
  String get lblEndServicesMsg => "Voulez-vous mettre fin à ce service?";

  @override
  String get lblCancelBooking => "Annuler la réservation";

  @override
  String get lblStart => "Démarrer";

  @override
  String get lblHold => "Tenir";

  @override
  String get lblResume => "Continuer";

  @override
  String get lblPayNow => "Payez maintenant";

  @override
  String get lblCheckStatus => "État de contrôle";

  @override
  String get lblID => "identifiant";

  @override
  String get lblNoBookingsFound => "Aucune réservation trouvée";

  @override
  String get lblCategory => "Catégorie";

  @override
  String get lblYourComment => "Votre commentaire";

  @override
  String get lblIntroducingCustomerRating => "Présentation de la notation client";

  @override
  String get lblSeeYourRatings => "Voir vos évaluations";

  @override
  String get lblFeatured => "Mis en exergue";

  @override
  String get lblNoServicesFound => "Aucun service trouvé";

  @override
  String get lblGENERAL => "GÉNÉRAL";

  @override
  String get lblAboutApp => "À propos de l'application";

  @override
  String get lblPurchaseCode => "Achat code source complet";

  @override
  String get lblNoRateYet => "Actuellement, vous n'avez pas évalué de services";

  @override
  String get lblMemberSince => "Membre depuis";

  @override
  String get lblFilterBy => "Filtrer par";

  @override
  String get lblClearFilter => "Filtre clair";

  @override
  String get lblNoReviews => "Aucun commentaire";

  @override
  String get lblUnreadNotification => "Notification non lu";

  @override
  String get lblChoosePaymentMethod => "Choisissez le mode de paiement";

  @override
  String get lblNoPayments => "Pas de paiements";

  @override
  String get lblPayWith => "Voulez-vous payer avec";

  @override
  String get payWith => "Payer avec";

  @override
  String get lblYourRating => "Votre note";

  @override
  String get lblEnterReview => "Entrez votre avis (facultatif)";

  @override
  String get lblDelete => "Supprimer";

  @override
  String get lblDeleteRatingMsg => 'Voulez-vous supprimer cette note?';

  @override
  String get lblSelectRating => 'La notation est requise';

  @override
  String get lblNoServiceRatings => "Aucune note de service";

  @override
  String get lblSearchFor => "Rechercher";

  @override
  String get lblRating => "Notation";

  @override
  String get lblAvailableAt => "Disponible à";

  @override
  String get lblRelatedServices => "Services associés";

  @override
  String get lblBookNow => "Reserve maintenant";

  @override
  String get lblWelcomeToHandyman => "Bienvenue à $APP_NAME";

  @override
  String get lblWalkThroughSubTitle => "Service $APP_NAME - Application de services à domicile à la demande avec solution complète";

  @override
  String get textHandyman => "Handyman";

  @override
  String get lblChooseFromMap => "Choisir parmi la carte";

  @override
  String get lblDeleteAddress => "Supprimer l'adresse";

  @override
  String get lblDeleteSunTitle => 'Voulez-vous supprimer cette adresse ?';

  @override
  String get lblFaq => "Faqs";

  @override
  String get lblServiceFaq => "FAQ sur le service";

  @override
  String get lblLogoutTitle => "Oh non, tu pars!";

  @override
  String get lblLogoutSubTitle => "Voulez-vous vous déconnecter?";

  @override
  String get lblFeaturedProduct => "Ceci est produit en vedette";

  @override
  String get lblAlert => "Alerte";

  @override
  String get lblOnBase => "Sur la base de";

  @override
  String get lblInvalidCoupon => "Le code de coupon est invalide";

  @override
  String get lblSelectCode => "Veuillez sélectionner le code de coupon";

  @override
  String get lblBackPressMsg => "Appuyez à nouveau sur Retour pour quitter l'application";

  @override
  String get lblHour => "heure";

  @override
  String get lblHelplineNumber => "Numéro d'assistance téléphonique";

  @override
  String get lblSubcategories => "Sous-catégories";

  @override
  String get lblAgree => "je suis d'accord avec le";

  @override
  String get lblTermsOfService => "Conditions d'utilisation";

  @override
  String get lblWalkThrough0 => "Service à bricoleur - Application de services domestiques à la demande avec solution complète";

  @override
  String get lblServiceTotalTime => "Temps total du service";

  @override
  String get lblDateTimeUpdated => 'Votre date et heure de réservation sont terminées avec succès';

  @override
  String get lblSelectDate => "Veuillez sélectionner l'heure de date";

  @override
  String get lblReasonCancelling => "Raison:";

  @override
  String get lblReasonRejecting => "Raison";

  @override
  String get lblFailed => "Raison pour laquelle cette réservation est échouée";

  @override
  String get lblNotDescription => "Pas de description disponible";

  @override
  String get lblMaterialTheme => "Activer le thème du matériel";

  @override
  String get lblServiceProof => "Preuve de service";

  @override
  String get lblAndroid12Support => "Cette action redémarrera votre application. Confirmer?";

  @override
  String get lblOff => "Remise";

  @override
  String get lblHr => "heure";

  @override
  String get lblSignInWithGoogle => "Connectez-vous avec Google";

  @override
  String get lblSignInWithOTP => "Connectez-vous avec OTP";

  @override
  String get lblDangerZone => "Zone dangereuse";

  @override
  String get lblDeleteAccount => "Supprimer le compte";

  @override
  String get lblUnderMaintenance => "En maintenance...";

  @override
  String get lblCatchUpAfterAWhile => "Rattraper après un moment";

  @override
  String get lblId => "Identifiant";

  @override
  String get lblMethod => "Méthode";

  @override
  String get lblStatus => "Statut";

  @override
  String get lblPending => "En attente";

  @override
  String get confirmationRequestTxt => 'Voulez-vous effectuer cette action?';

  @override
  String get lblDeleteAccountConformation => "Votre compte sera supprimé en permanence. Vos données ne seront plus restaurées.";

  @override
  String get lblAutoSliderStatus => "Statut de curseur automatique";

  @override
  String get lblPickAddress => "Choisir l'adresse";

  @override
  String get lblUpdateDateAndTime => "Date et heure de mise à jour";

  @override
  String get lblRecheck => "Revérifier";

  @override
  String get lblLoginAgain => "Veuillez vous connecter à nouveau";

  @override
  String get lblUpdate => "Mise à jour";

  @override
  String get lblNewUpdate => "Nouvelle mise à jour";

  @override
  String get lblOptionalUpdateNotify => "Mise à jour facultative notification";

  @override
  String get lblAnUpdateTo => "Une mise à jour de";

  @override
  String get lblIsAvailableWouldYouLike => "est disponible. Voulez-vous mettre à jour?";

  @override
  String get lblRegisterAsPartner => "S'inscrire comme partenaire";

  @override
  String get lblSignInWithApple => "Connectez-vous avec Apple";

  @override
  String get lblWaitingForProviderApproval => "En attente d'approbation du fournisseur";

  @override
  String get lblFree => "non payé";

  @override
  String get lblAppleSignInNotAvailable => "Apple Signin n'est pas disponible pour votre appareil";

  @override
  String get lblTotalExtraCharges => "Total des frais supplémentaires";

  @override
  String get lblWaitingForResponse => "En attente d'une réponse";

  @override
  String get lblAll => "Tout";

  @override
  String get noConversation => "Aucune conversation";

  @override
  String get noConversationSubTitle => "Vous n'avez pas encore fait de conversation. Remplissez un service pour discuter avec un fournisseur.";

  @override
  String get noBookingSubTitle => "On dirait que vous n'avez pas encore réservé votre commande";

  @override
  String get myReviews => "Mes critiques";

  @override
  String get noCategoryFound => "Aucune catégorie trouvée";

  @override
  String get noProviderFound => "Aucun fournisseur trouvé";

  @override
  String get createServiceRequest => "Créer une demande de service";

  @override
  String get chooseImages => "Choisir des images";

  @override
  String get serviceDescription => "Description du service";

  @override
  String get addNewService => "Ajouter un nouveau service";

  @override
  String get newPostJobRequest => "Publier une nouvelle demande d'emploi";

  @override
  String get postJobTitle => "Publier un titre";

  @override
  String get postJobDescription => "Description de poste de poste";

  @override
  String get services => "Prestations de service";

  @override
  String get myPostJobList => "Ma demande de travail personnalisée";

  @override
  String get requestNewJob => "Demander un nouvel emploi";

  @override
  String get noNotifications => "Aucune notification";

  @override
  String get noNotificationsSubTitle => "Nous vous informerons une fois que nous aurons quelque chose pour vous";

  @override
  String get noFavouriteSubTitle => "Vos services préférés apparaîtront ici";

  @override
  String get termsConditionsAccept => "Veuillez accepter les termes et conditions";

  @override
  String get disclaimer => "Clause de non-responsabilité";

  @override
  String get disclaimerContent => "On vous demandera le paiement une fois votre réservation terminée.";

  @override
  String get inputMustBeNumberOrDigit => 'L\'entrée doit être le numéro ou le chiffre';

  @override
  String get requiredAfterCountryCode => 'requis après le code du pays';

  @override
  String get selectedOtherBookingTime => 'Le temps de réservation sélectionné est déjà passé. Veuillez sélectionner une autre fois.';

  @override
  String get myServices => 'Mes services';

  @override
  String get doYouWantToAssign => 'Voulez-vous attribuer';

  @override
  String get bidPrice => 'Prix ​​de l\'offre';

  @override
  String get accept => 'Accepter';

  @override
  String get price => 'Prix';

  @override
  String get remove => 'Retirer';

  @override
  String get add => 'Ajouter';

  @override
  String get save => 'sauvegarder';

  @override
  String get createPostJobWithoutSelectService => 'Vous ne pouvez pas créer de poste de poste sans sélectionner le service';

  @override
  String get selectCategory => 'Choisir une catégorie';

  @override
  String get pleaseAddImage => 'Veuillez ajouter l\'image';

  @override
  String get selectedBookingTimeIsAlreadyPassed => 'Le temps de réservation sélectionné est déjà passé. Veuillez sélectionner une autre fois.';

  @override
  String get jobPrice => 'Prix ​​de l\'emploi';

  @override
  String get estimatedPrice => 'Prix ​​estimé';

  @override
  String get bidder => 'Soumissionnaire';

  @override
  String get assignedProvider => 'Fournisseur assigné';

  @override
  String get myPostDetail => 'Mon détail';

  @override
  String get thankYou => 'Merci!';

  @override
  String get bookingConfirmedMsg => 'Votre réservation est confirmée.';

  @override
  String get goToHome => 'Aller à la maison';

  @override
  String get goToReview => "Aller à l'examen";

  @override
  String get noServiceAdded => 'Aucun service ajouté';

  @override
  String get noPostJobFound => 'Aucun poste de poste trouvé';

  @override
  String get noPostJobFoundSubtitle => 'Lorsque vous publiez votre travail, chaque fournisseur sera informé et vous pouvez choisir votre fournisseur souhaité pour faire le travail.';

  @override
  String get pleaseEnterValidOTP => 'Veuillez saisir OTP valide';

  @override
  String get confirmOTP => 'Confirmer OTP';

  @override
  String get sendingOTP => 'Envoi OTP';

  @override
  String get pleaseSelectDifferentSlotThenPrevious => '""Veuillez sélectionner différents emplacements puis précédent';

  @override
  String get pleaseSelectTheSlotsFirst => "Veuillez d'abord sélectionner les emplacements";

  @override
  String get editTimeSlotsBooking => 'Modifier la réservation de créneaux horaires';

  @override
  String get availableSlots => 'Emplacements disponibles';

  @override
  String get noTimeSlots => 'Pas de plages horaires';

  @override
  String get bookingDateAndSlot => 'Date de datte et créneau';

  @override
  String get extraCharges => 'Frais supplémentaires';

  @override
  String get chatCleared => 'Chat effacé';

  @override
  String get clearChat => 'Chat claire';

  @override
  String get jobRequestSubtitle => 'Je ne trouve pas votre service? Ne vous inquiétez pas, vous pouvez publier vos besoins.';

  @override
  String get verified => 'Vérifié';

  @override
  String get theEnteredCodeIsInvalidPleaseTryAgain => "Le code entré n'est pas valide, veuillez réessayer";

  @override
  String get otpCodeIsSentToYourMobileNumber => 'Le code OTP est envoyé à votre numéro de mobile';

  @override
  String get yourPaymentFailedPleaseTryAgain => 'Votre paiement a échoué, veuillez réessayer';

  @override
  String get yourPaymentHasBeenMadeSuccessfully => 'Votre paiement a été effectué avec succès';

  @override
  String get transactionFailed => 'La transaction a échoué';

  @override
  String get lblStep3 => 'Étape 3';

  @override
  String get lblAvailableOnTheseDays => 'Disponible ces jours-ci';

  @override
  String get internetNotAvailable => 'Votre Internet semble être hors ligne';

  @override
  String get pleaseTryAgain => 'Veuillez réessayer';

  @override
  String get somethingWentWrong => "Quelque chose s'est mal passé";

  @override
  String get postJob => 'Poste de poste';

  @override
  String get package => 'Forfait';

  @override
  String get frequentlyBoughtTogether => 'Produits fréquemment achetés ensemble';

  @override
  String get endOn => 'Se termine';

  @override
  String get buy => 'Acheter';

  @override
  String get includedServices => 'Services inclus';

  @override
  String get includedInThisPackage => 'Inclus dans ce package';

  @override
  String get lblInvalidTransaction => 'Transaction invalide';

  @override
  String get getTheseServiceWithThisPackage => 'Vous obtiendrez ces services avec ce package';

  @override
  String get lblNotValidUser => "Vous n'êtes pas un utilisateur valide";

  @override
  String get lblSkip => 'Sauter';

  @override
  String get lblChangeCountry => 'Changer de pays';

  @override
  String get lblTimeSlotNotAvailable => 'Cette fente n\'est pas disponible';

  @override
  String get lblAdd => 'ajouter';

  @override
  String get lblThisService => 'ce service';

  @override
  String get lblYourCurrenciesNotSupport => 'Vos devises ne prennent pas en charge Cinetpay';

  @override
  String get lblSignInFailed => 'La connexion a échoué';

  @override
  String get lblUserCancelled => 'Utilisateur annulé';

  @override
  String get lblTransactionCancelled => 'Transaction annulée';

  @override
  String get lblExample => 'Exemple';

  @override
  String get lblCheckOutWithCinetPay => 'Découvrez avec Cinetpay';

  @override
  String get lblLocationPermissionDenied => 'Les autorisations de localisation sont refusées.';

  @override
  String get lblLocationPermissionDeniedPermanently => 'Les autorisations de localisation sont refusées en permanence, nous ne pouvons pas demander des autorisations.';

  @override
  String get lblEnableLocation => 'Veuillez vous assurer que les services de localisation sont activés.';

  @override
  String get lblNoUserFound => 'Aucun utilisateur trouvé';

  @override
  String get lblUserNotCreated => 'Utilisateur non créé';

  @override
  String get lblTokenExpired => 'Jeton expiré';

  @override
  String get lblConfirmationForDeleteMsg => 'Voulez-vous supprimer le message ?';

  @override
  String get favouriteProvider => 'Fournisseur préféré';

  @override
  String get noProviderFoundMessage => 'Vos fournisseurs préférés apparaîtront ici';

  @override
  String get personalInfo => 'Informations personnelles';

  @override
  String get essentialSkills => 'Compétences essentielles';

  @override
  String get knownLanguages => 'Langues connues';

  @override
  String get authorBy => 'Auteur';

  @override
  String get views => 'Vues';

  @override
  String get blogs => 'Blogs';

  @override
  String get noBlogsFound => 'Aucun blog trouvé';

  @override
  String get requestInvoice => 'Demande la facture';

  @override
  String get invoiceSubTitle => 'Entrez l\'adresse e-mail où vous souhaitez recevoir votre facture';

  @override
  String get sentInvoiceText => 'Veuillez vérifier votre e-mail que nous avons envoyé une facture sur votre e-mail.';

  @override
  String get send => 'Envoyer';

  @override
  String get published => 'Publié';

  @override
  String get clearChatMessage => 'Voulez-vous effacer cette discussion?';

  @override
  String get deleteMessage => 'Voulez-vous supprimer?';

  @override
  String get accepted => 'Accepté';

  @override
  String get onGoing => 'En cours';

  @override
  String get inProgress => 'En cours';

  @override
  String get cancelled => 'Annulé';

  @override
  String get rejected => 'Rejeté';

  @override
  String get failed => 'Échoué';

  @override
  String get completed => 'Complété';

  @override
  String get pendingApproval => 'En attente de validation';

  @override
  String get waiting => 'En attendant';

  @override
  String get paid => 'Payé';

  @override
  String get advancePaid => 'Avance';

  @override
  String get insufficientBalanceMessage => 'Vous avez un équilibre insuffisant dans votre portefeuille. Veuillez choisir une autre méthode.';

  @override
  String get cinetPayNotSupportedMessage => "Cinetpay n'est pas pris en charge par vos devises";

  @override
  String get loading => 'Chargement..';

  @override
  String get walletBalance => 'Équilibre du portefeuille';

  @override
  String get payAdvance => 'Avance';

  @override
  String get advancePaymentMessage => "Effectuer un paiement à l'avance pour compléter une réservation";

  @override
  String get advancePayAmount => "Montant de rémunération à l'avance";

  @override
  String get remainingAmount => 'Montant restant';

  @override
  String get advancePayment => 'Acompte';

  @override
  String get withExtraAndAdvanceCharge => "Avec des frais supplémentaires et un paiement à l'avance";

  @override
  String get withExtraCharge => 'Avec un frais supplémentaire';

  @override
  String get min => 'min';

  @override
  String get hour => 'heure';

  @override
  String get customerRatingMessage => 'Dites aux autres ce que vous pensez';

  @override
  String get paymentHistory => 'historique de paiement';

  @override
  String get message => 'Message';

  @override
  String get wallet => 'Portefeuille';

  @override
  String get payWithFlutterWave => 'Payer avec Flutterwave';

  @override
  String get goodMorning => 'Bonjour';

  @override
  String get goodAfternoon => 'Bon après-midi';

  @override
  String get goodEvening => 'Bonne soirée';

  @override
  String get invalidURL => 'URL invalide';

  @override
  String get use24HourFormat => 'Utiliser le format 24 heures ?';

  @override
  String get email => 'E-mail';

  @override
  String get badRequest => 'Mauvaise demande';

  @override
  String get forbidden => 'interdit';

  @override
  String get pageNotFound => 'Page non trouvée';

  @override
  String get tooManyRequests => 'trop de demandes';

  @override
  String get internalServerError => 'Erreur du serveur interne';

  @override
  String get badGateway => 'Mauvaise passerelle';

  @override
  String get serviceUnavailable => 'Service Indisponible';

  @override
  String get gatewayTimeout => 'portail expiré';

  @override
  String get pleaseWait => "S'il vous plaît, attendez";

  @override
  String get externalWallet => 'Portefeuille externe';

  @override
  String get userNotFound => 'Utilisateur non trouvé';

  @override
  String get requested => 'Demandé';

  @override
  String get assigned => 'Attribué';

  @override
  String get reload => 'Recharger';

  @override
  String get lblStripeTestCredential => "Le test des informations d'identification ne peut pas payer plus que 500";

  @override
  String get noDataFoundInFilter => 'Choisissez les meilleurs critères de filtre pour obtenir les meilleurs résultats';

  @override
  String get addYourCountryCode => 'Ajoutez votre code de pays';

  @override
  String get help => 'Aider';

  @override
  String get couponCantApplied => 'Ce coupon ne peut pas être appliqué';

  @override
  String get priceAmountValidationMessage => 'Le montant du prix doit être râpé que 0';

  @override
  String get pleaseWaitWhileWeLoadChatDetails => 'Veuillez patienter pendant que nous chargeons les détails du chat';

  @override
  String get isNotAvailableForChat => "'n'est pas disponible pour le chat'";

  @override
  String get connectWithFirebaseForChat => "Verbinden Sie sich mit Firebase für den Cha";

  @override
  String get closeApp => "Fermer l'application";

  @override
  String get providerAddedToFavourite => 'Fournisseur ajouté à la liste préférée';

  @override
  String get providerRemovedFromFavourite => 'Fournisseur supprimé de la liste préférée';

  @override
  String get provideValidCurrentPasswordMessage => 'Vous devez fournir un mot de passe actuel valide';

  @override
  String get copied => 'Copié';

  @override
  String get copyMessage => 'Copier le message';

  @override
  String get messageDelete => 'Supprimer le message';

  @override
  String get pleaseChooseAnyOnePayment => "Veuillez choisir n'importe quel mode de paiement";

  @override
  String get myWallet => 'Mon portefeuille';

  @override
  String get balance => 'Équilibre';

  @override
  String get topUpWallet => "Portefeuille d'allumage";

  @override
  String get topUpAmountQuestion => 'Quel montant préféreriez-vous à recharger?';

  @override
  String get paymentMethod => 'Mode de paiement';

  @override
  String get selectYourPaymentMethodToAddBalance => 'Sélectionnez votre mode de paiement pour ajouter le solde';

  @override
  String get proceedToTopUp => 'Procéder à la recharge';

  @override
  String get serviceAddedToFavourite => 'Service ajouté à la liste préférée';

  @override
  String get serviceRemovedFromFavourite => 'Service supprimé de la liste préférée';

  @override
  String get firebaseRemoteCannotBe => 'La télécommande de la base de feu ne peut pas être connectée';

  @override
  String get search => 'Recherche';

  @override
  String get close => 'Fermer';

  @override
  String get totalAmountShouldBeMoreThan => 'Le montant total devrait être plus que';

  @override
  String get totalAmountShouldBeLessThan => 'Le montant total devrait être inférieur à';

  @override
  String get doYouWantToTopUpYourWallet => 'Voulez-vous recharger votre portefeuille maintenant?';

  @override
  String get chooseYourLocation => 'Choisissez votre emplacement';

  @override
  String get connect => 'Connecter';

  @override
  String get transactionId => 'identifiant de transaction';

  @override
  String get at => 'à';

  @override
  String get appliedTaxes => 'Taxes appliquées';

  @override
  String get accessDeniedContactYourAdmin => "Accès refusé. Contactez votre administrateur pour obtenir de l'aide.";

  @override
  String get yourWalletIsUpdated => 'Votre portefeuille est mis à jour!';

  @override
  String get by => 'par';

  @override
  String get noPaymentMethodFound => 'Aucun mode de paiement trouvé';

  @override
  String get theAmountShouldBeEntered => 'Le montant doit être saisi';

  @override
  String get walletHistory => 'Histoire du portefeuille';

  @override
  String get debit => 'Débit';

  @override
  String get credit => 'Crédit';

  @override
  String get youCannotApplyThisCoupon => 'Vous ne pouvez pas appliquer ce coupon';

  @override
  String get basedOn => 'Basé sur';

  @override
  String get serviceStatusPicMessage => 'Veuillez vous assurer de choisir au moins un statut de réservation';

  @override
  String get clearFilter => 'Filtre effacer';

  @override
  String get bookingStatus => 'Statut de réservation';

  @override
  String get addOns => 'Complémentations';

  @override
  String get serviceAddOns => 'Service complémentaires';

  @override
  String get turnOn => 'Allumer';

  @override
  String get turnOff => 'Éteindre';

  @override
  String get serviceVisitType => 'Servicebesuchstyp';

  @override
  String get thisServiceIsOnlineRemote => 'Ce service sera terminé en ligne / à distance.';

  @override
  String get deleteMessageForAddOnService => 'Möchten Sie diesen Add-On-Service entfernen?';

  @override
  String get confirmation => 'Confirmation!';

  @override
  String get pleaseNoteThatAllServiceMarkedCompleted => 'Veuillez noter que tous les modules complémentaires de service marqués comme terminés!';

  @override
  String get writeHere => 'Écrivez ici';

  @override
  String get isAvailableGoTo => "est disponible. Allez à Play Store et téléchargez la nouvelle version de l'application.";

  @override
  String get later => 'Plus tard';

  @override
  String get whyChooseMe => 'Pourquoi me choisir?';

  @override
  String get useThisCodeToGet => 'Utilisez ce code pour obtenir';

  @override
  String get off => 'désactivé';

  @override
  String get applied => 'Appliqué';

  @override
  String get coupons => 'Coupons';

  @override
  String get handymanList => 'Liste de bricoles';

  @override
  String get noHandymanFound => 'Aucun bricoleur trouvé';

  @override
  String get back => 'Dos';

  @override
  String get team => 'Équipe';

  @override
  String get whyChooseMeAs => 'Pourquoi me choisir comme fournisseur de services de confiance';

  @override
  String get reason => 'Raison';

  @override
  String get pleaseEnterAddressAnd => "Veuillez saisir l'adresse et la date de réservation et la fente";

  @override
  String get pleaseEnterYourAddress => 'Veuillez entrer votre adresse';

  @override
  String get pleaseSelectBookingDate => 'Veuillez sélectionner la date de réservation et la fente';

  @override
  String get doYouWantTo => 'Voulez-vous supprimer ce coupon?';

  @override
  String get chooseDateTime => "Choisissez la date et l'heure";

  @override
  String get airtelMoneyPayment => 'Paiement en argent Airtel';

  @override
  String get recommendedForYou => 'Recommandé pour vous';

  @override
  String get paymentSuccess => 'Succès du paiement';

  @override
  String get redirectingToBookings => 'Redirection vers les réservations.';

  @override
  String get transactionIsInProcess => 'La transaction est en cours ...';

  @override
  String get pleaseCheckThePayment => 'Veuillez vérifier que la demande de paiement est envoyée à votre numéro';

  @override
  String get enterYourMsisdnHere => 'Entrez votre msisdn ici';

  @override
  String get theTransactionIsStill => "La transaction est toujours en train de traiter et est à l'état ambigu. Veuillez effectuer la demande de transaction pour récupérer l'état de la transaction.";

  @override
  String get transactionIsSuccessful => 'La transaction est réussie';

  @override
  String get incorrectPinHasBeen => 'Une broche incorrecte a été entrée';

  @override
  String get theUserHasExceeded => "L'utilisateur a dépassé sa limite de transaction autorisée par portefeuille";

  @override
  String get theAmountUserIs => "Le montant que l'utilisateur essaie de transférer est inférieur au montant minimum autorisé";

  @override
  String get userDidnTEnterThePin => "L'utilisateur n'est pas entré dans la broche";

  @override
  String get transactionInPendingState => "Transaction à l'état en attente. Veuillez vérifier après un certain temps";

  @override
  String get userWalletDoesNot => "Le portefeuille d'utilisateurs n'a pas assez d'argent pour couvrir le montant payable";

  @override
  String get theTransactionWasRefused => 'La transaction a été refusée';

  @override
  String get thisIsAGeneric => "C'est un refus générique qui a plusieurs causes possibles";

  @override
  String get payeeIsAlreadyInitiated => 'Le bénéficiaire est déjà lancé pour le désabonnement ou les barrages ou non enregistrés sur la plate-forme Airtel Money';

  @override
  String get theTransactionWasTimed => 'La transaction a été chronométrée.';

  @override
  String get theTransactionWasNot => "La transaction n'a pas été trouvée.";

  @override
  String get xSignatureAndPayloadDid => 'La signature X et la charge utile ne correspondaient pas';

  @override
  String get encryptionKeyHasBeen => 'La clé de chiffrement a été récupérée avec succès';

  @override
  String get couldNotFetchEncryption => 'Impossible de récupérer la clé de chiffrement';

  @override
  String get transactionHasBeenExpired => 'La transaction a été expirée';

  @override
  String get ambiguous => 'Ambiguë';

  @override
  String get success => 'Succès';

  @override
  String get incorrectPin => 'Broche incorrecte';

  @override
  String get exceedsWithdrawalAmountLimitS => 'Dépasse la limite du montant du retrait / la limite de montant de retrait dépassée';

  @override
  String get invalidAmount => 'Montant non valide';

  @override
  String get transactionIdIsInvalid => "L'ID de transaction n'est pas valide";

  @override
  String get inProcess => 'En cours';

  @override
  String get notEnoughBalance => "Pas assez d'équilibre";

  @override
  String get refused => 'Refusé';

  @override
  String get doNotHonor => "N'honore pas";

  @override
  String get transactionNotPermittedTo => 'Transaction non autorisée au bénéficiaire';

  @override
  String get transactionTimedOut => 'Transaction expirée';

  @override
  String get transactionNotFound => 'Transaction introuvable';

  @override
  String get forBidden => 'Interdit';

  @override
  String get successfullyFetchedEncryptionKey => 'Clé de chiffrement récupéré avec succès';

  @override
  String get errorWhileFetchingEncryption => 'Erreur tout en récupérant la clé de chiffrement';

  @override
  String get transactionExpired => 'La transaction a expiré';

  @override
  String get verifyEmail => "Vérifier l'e-mail";

  @override
  String get minRead => 'Min Read';

  @override
  String get loadingChats => 'Charger des chats ...';

  @override
  String get monthly => 'Mensuel';

  @override
  String get noCouponsAvailableMsg => 'Pas de coupons pour le moment. Continuez à vérifier les offres exclusives!';

  @override
  String get refundPolicy => 'Politique de remboursement';

  @override
  String get chooseAnyOnePayment => "Choisissez d'abord n'importe quel mode de paiement";

  @override
  String get january => 'Janvier';

  @override
  String get february => 'Février';

  @override
  String get march => 'Mars';

  @override
  String get april => 'Avril';

  @override
  String get may => 'Peut';

  @override
  String get june => 'Juin';

  @override
  String get july => 'Juillet';

  @override
  String get august => 'Août';

  @override
  String get september => 'Septembre';

  @override
  String get october => 'Octobre';

  @override
  String get november => 'Novembre';

  @override
  String get december => 'Décembre';

  @override
  String get monthName => 'Nom du mois';

  @override
  String get mon => 'Lun';

  @override
  String get tue => 'Mar';

  @override
  String get wed => 'Épouser';

  @override
  String get thu => 'Jeu';

  @override
  String get fri => 'Ven';

  @override
  String get sat => 'Assis';

  @override
  String get sun => 'Soleil';

  @override
  String get weekName => 'Nom de la semaine';

  @override
  String get removeThisFile => 'Supprimer ce fichier';

  @override
  String get areYouSureWantToRemoveThisFile => 'Voulez-vous supprimer ce fichier?';

  @override
  String get sendMessage => 'Envoyer le message';

  @override
  String get youAreNotConnectedWithChatServer => 'Connectez-vous au serveur de discussion';

  @override
  String get NotConnectedWithChatServerMessage => "Vous n'êtes pas connecté au serveur de chat. Appuyez sur le bouton ci-dessous pour vous connecter et commencer à discuter";

  @override
  String get sentYouAMessage => 'Vous a envoyé un message';

  @override
  String get pushNotification => 'Notification push';

  @override
  String get yourBooking => 'Votre réservation';

  @override
  String get featuredServices => 'Services en vedette';

  @override
  String get postYourRequestAnd => 'Postez votre demande et nous ferons de notre mieux pour y répondre';

  @override
  String get newRequest => 'Nouvelle requête';

  @override
  String get upcomingBooking => 'Réservation à venir';

  @override
  String get theUserHasDenied => "L'utilisateur a refusé l'utilisation de la reconnaissance vocale";

  @override
  String get helloGuest => 'Bonjour invité';

  @override
  String get eGCleaningPlumberPest => 'par exemple. nettoyage, plombier, lutte antiparasitaire';

  @override
  String get ifYouDidnTFind => "Si vous n'avez pas trouvé notre service, ne vous inquiétez pas ! Vous pouvez facilement poster votre demande.";

  @override
  String get popularServices => 'Services populaires';

  @override
  String get canTFindYourServices => 'Vous ne trouvez pas vos services ?';

  @override
  String get trackProviderLocation => 'Localisation du fournisseur de suivi';

  @override
  String get trackHandymanLocation => "Suivre l'emplacement du bricoleur";

  @override
  String get handymanLocation => 'Emplacement du bricoleur';

  @override
  String get providerLocation => 'Emplacement du fournisseur';

  @override
  String get lastUpdatedAt => 'Dernière mise à jour à :';

  @override
  String get track => 'Piste';

  @override
  String get handymanReached => 'Bricoleur atteint ? Cliquez pour commencer';

  @override
  String get providerReached => 'Fournisseur atteint ? Cliquez pour commencer';

  @override
  String get lblBankDetails => "Coordonnées bancaires";

  @override
  String get addBank => "Ajouter une banque";

  @override
  String get bankList => "Liste des banques";

  @override
  String get lbldefault => "Défaut";

  @override
  String get setAsDefault => "Définir par défaut";

  @override
  String get aadharNumber => "Numéro Aadhar";

  @override
  String get panNumber => "Numéro PAN";

  @override
  String get lblPleaseEnterAccountNumber => "Veuillez entrer le numéro de compte";

  @override
  String get lblAccountNumberMustContainOnlyDigits => "Le numéro de compte ne doit contenir que des chiffres";

  @override
  String get lblAccountNumberMustBetween11And16Digits => "Le numéro de compte doit comporter entre 11 et 16 chiffres";

  @override
  String get noBankDataTitle => "Aucune donnée bancaire trouvée";

  @override
  String get noBankDataSubTitle => "Vous n'avez pas encore ajouté de banque";

  @override
  String get active => 'Actif';

  @override
  String get inactive => 'Inactif';

  @override
  String get deleteBankTitle => 'Voulez-vous supprimer cette banque?';

  @override
  String get bankName => 'Nom de banque';

  @override
  String get accountNumber => 'Numéro de compte';

  @override
  String get iFSCCode => 'Code IFSC';

  @override
  String get lblEdit => 'Éditer';

  @override
  String get availableBalance => "Solde disponible";

  @override
  String get withdraw => "Retirer";

  @override
  String get successful => 'Réussi';

  @override
  String get yourWithdrawalRequestHasBeenSuccessfullySubmitted => 'Votre demande de retrait a été soumise avec succès.';

  @override
  String get eg3000 => 'par exemple "3000"';

  @override
  String get chooseBank => "Choisir la banque";

  @override
  String get egCentralNationalBank => 'par exemple "banque centrale nationale"';

  @override
  String get topUp => "Recharge";

  @override
  String get lblEnterAmount => "Entrer le montant";

  @override
  String get withdrawRequest => "Demande de retrait";

  @override
  String get pleaseAddLessThanOrEqualTo => "Veuillez ajouter inférieur ou égal à";

  @override
  String get btnSave => 'sauvegarder';

  @override
  String get fullNameOnBankAccount => 'Nom complet sur le compte bancaire';

  @override
  String get packageIsExpired => 'Le colis est expiré';

  @override
  String get bookPackage => 'Forfait Livre';

  @override
  String get packageDescription => 'Description du paquet';

  @override
  String get packagePrice => 'Prix du forfait';

  @override
  String get online => 'En ligne';

  @override
  String get noteAddressIsNot => "Remarque: L'adresse n'est pas requise pour les services distants.";

  @override
  String get wouldYouLikeTo => 'Souhaitez-vous poursuivre et confirmer cette réservation ?';

  @override
  String get packageName => 'Nom du paquet';

  @override
  String get feeAppliesForCancellations => "des frais s'appliquent pour les annulations effectuées dans les";

  @override
  String get a => 'UN';

  @override
  String get byConfirmingYouAgree => 'En confirmant, vous acceptez notre';

  @override
  String get and => 'et';

  @override
  String get areYouSureYou => "Êtes-vous sûr de vouloir annuler ? Des frais d'annulation peuvent s'appliquer en fonction du prix de votre service";

  @override
  String get totalCancellationFee => "Frais d'annulation totaux";

  @override
  String get goBack => 'Retourner';

  @override
  String get bookingCancelled => 'Réservation annulée';

  @override
  String get yourBookingHasBeen => 'Votre réservation a été annulée avec succès. Le remboursement applicable sera traité dans les 24 heures';

  @override
  String get noteCheckYourBooking => 'Remarque : Vérifiez votre historique de réservation pour connaître les détails du remboursement.';

  @override
  String get cancelledReason => 'Motif annulé';

  @override
  String get refundPaymentDetails => 'Détails du paiement du remboursement';

  @override
  String get refundOf => 'Remboursement de';

  @override
  String get refundAmount => 'Montant du remboursement';

  @override
  String get cancellationFee => "Frais d'annulation";

  @override
  String get advancedPayment => 'Paiement anticipé';

  @override
  String get hoursOfTheScheduled => 'heures du service prévu';

  @override
  String get open => 'OUVRIR';

  @override
  String get closed => 'FERMÉ';

  @override
  String get createBy => 'Créer par';

  @override
  String get repliedBy => 'Répondu par';

  @override
  String get closedBy => 'Fermé par';

  @override
  String get helpDesk => "Service d'assistance";

  @override
  String get addNew => 'Ajouter un nouveau';

  @override
  String get queryYet => 'Requête déjà';

  @override
  String get toSubmitYourProblems => 'Pour soumettre vos problèmes, appuyez simplement sur le bouton Ajouter et expliquez votre préoccupation.';

  @override
  String get noRecordsFoundFor => 'Aucun enregistrement trouvé pour';

  @override
  String get queries => 'requêtes.';

  @override
  String get noActivityYet => "Aucune activité pour l'instant";

  @override
  String get noRecordsFound => 'Aucun enregistrement trouvé';

  @override
  String get reply => 'Répondre';

  @override
  String get eGDuringTheService => 'par ex. Pendant le service, le mobilier a été accidentellement endommagé.';

  @override
  String get doYouWantClosedThisQuery => 'Voulez-vous fermer cette requête';

  @override
  String get markAsClosed => 'Marquer comme fermé';

  @override
  String get youCanMarkThis => 'Vous pouvez marquer ceci comme fermé si vous êtes satisfait de notre réponse';

  @override
  String get subject => 'Sujet';

  @override
  String get eGDamagedFurniture => 'par ex. Meubles endommagés';

  @override
  String get closedOn => 'Fermé le :';

  @override
  String get on => 'sur';

  @override
  String get showMessage => 'Afficher le message';

  @override
  String get yesterday => 'Hier';

  @override
  String get chooseAction => 'Choisir une action';

  @override
  String get chooseImage => 'Choisir une image';

  @override
  String get noteYouCanUpload => "Remarque : Vous pouvez télécharger une image avec les extensions \'jpg\', \'png\', \'jpeg\' et vous ne pouvez sélectionner qu'une seule image.";

  @override
  String get removeImage => "Supprimer l'image";

  @override
  String get advancedRefund => 'Remboursement avancé';

  @override
  String get lblService => 'Service';

  @override
  String get dateRange => 'Plage de dates';

  @override
  String get paymentType => 'Type de paiement';

  @override
  String get reset => 'Réinitialiser';

  @override
  String get noStatusFound => 'Aucun statut trouvé';

  @override
  String get selectStartDateEndDate => 'Sélectionnez la date de début et la date de fin';

  @override
  String get handymanNotFound => 'Bricoleur introuvable';

  @override
  String get providerNotFound => 'Fournisseur introuvable';

  @override
  String get rateYourExperience => 'Évaluez votre expérience';

  @override
  String get weValueYourFeedback => 'Nous apprécions vos commentaires ! Veuillez évaluer votre expérience récente avec notre service';

  @override
  String get viewStatus => "Afficher l'état";

  @override
  String get paymentInfo => 'Informations de paiement';

  @override
  String get mobile => 'Mobile:';

  @override
  String get to => 'à';

  @override
  String get chooseYourDateRange => 'Choisissez votre plage de dates';

  @override
  String get asHandyman => 'En tant que bricoleur';

  @override
  String get passwordLengthShouldBe => 'La longueur du mot de passe doit être comprise entre 8 et 12 caractères.';

  @override
  String get cash => 'Espèces';

  @override
  String get bank => 'Banque';

  @override
  String get razorPay => "RazorPay";

  @override
  String get payPal => "PayPal";

  @override
  String get stripe => "Stripe";

  @override
  String get payStack => "PayStack";

  @override
  String get flutterWave => "FlutterWave";

  @override
  String get paytm => "Paytm";

  @override
  String get airtelMoney => "Airtel Money";

  @override
  String get cinet => "Cinet";

  @override
  String get midtrans => "Midtrans";

  @override
  String get sadadPayment => "Sadad";

  @override
  String get phonePe => "PhonePe";

  @override
  String get inAppPurchase => "Achat intégré";

  @override
  String get pix => "Pix";

  @override
  String get chooseWithdrawalMethod => "Choisissez la méthode de retrait";
}