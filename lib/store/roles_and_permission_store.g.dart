// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'roles_and_permission_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$RolesAndPermissionStore on _RolesAndPermissionStore, Store {
  late final _$roleAtom =
      Atom(name: '_RolesAndPermissionStore.role', context: context);

  @override
  bool get role {
    _$roleAtom.reportRead();
    return super.role;
  }

  @override
  set role(bool value) {
    _$roleAtom.reportWrite(value, super.role, () {
      super.role = value;
    });
  }

  late final _$roleAddAtom =
      Atom(name: '_RolesAndPermissionStore.roleAdd', context: context);

  @override
  bool get roleAdd {
    _$roleAddAtom.reportRead();
    return super.roleAdd;
  }

  @override
  set roleAdd(bool value) {
    _$roleAddAtom.reportWrite(value, super.roleAdd, () {
      super.roleAdd = value;
    });
  }

  late final _$roleListAtom =
      Atom(name: '_RolesAndPermissionStore.roleList', context: context);

  @override
  bool get roleList {
    _$roleListAtom.reportRead();
    return super.roleList;
  }

  @override
  set roleList(bool value) {
    _$roleListAtom.reportWrite(value, super.roleList, () {
      super.roleList = value;
    });
  }

  late final _$permissionAtom =
      Atom(name: '_RolesAndPermissionStore.permission', context: context);

  @override
  bool get permission {
    _$permissionAtom.reportRead();
    return super.permission;
  }

  @override
  set permission(bool value) {
    _$permissionAtom.reportWrite(value, super.permission, () {
      super.permission = value;
    });
  }

  late final _$permissionAddAtom =
      Atom(name: '_RolesAndPermissionStore.permissionAdd', context: context);

  @override
  bool get permissionAdd {
    _$permissionAddAtom.reportRead();
    return super.permissionAdd;
  }

  @override
  set permissionAdd(bool value) {
    _$permissionAddAtom.reportWrite(value, super.permissionAdd, () {
      super.permissionAdd = value;
    });
  }

  late final _$permissionListAtom =
      Atom(name: '_RolesAndPermissionStore.permissionList', context: context);

  @override
  bool get permissionList {
    _$permissionListAtom.reportRead();
    return super.permissionList;
  }

  @override
  set permissionList(bool value) {
    _$permissionListAtom.reportWrite(value, super.permissionList, () {
      super.permissionList = value;
    });
  }

  late final _$categoryAtom =
      Atom(name: '_RolesAndPermissionStore.category', context: context);

  @override
  bool get category {
    _$categoryAtom.reportRead();
    return super.category;
  }

  @override
  set category(bool value) {
    _$categoryAtom.reportWrite(value, super.category, () {
      super.category = value;
    });
  }

  late final _$categoryAddAtom =
      Atom(name: '_RolesAndPermissionStore.categoryAdd', context: context);

  @override
  bool get categoryAdd {
    _$categoryAddAtom.reportRead();
    return super.categoryAdd;
  }

  @override
  set categoryAdd(bool value) {
    _$categoryAddAtom.reportWrite(value, super.categoryAdd, () {
      super.categoryAdd = value;
    });
  }

  late final _$categoryListAtom =
      Atom(name: '_RolesAndPermissionStore.categoryList', context: context);

  @override
  bool get categoryList {
    _$categoryListAtom.reportRead();
    return super.categoryList;
  }

  @override
  set categoryList(bool value) {
    _$categoryListAtom.reportWrite(value, super.categoryList, () {
      super.categoryList = value;
    });
  }

  late final _$categoryEditAtom =
      Atom(name: '_RolesAndPermissionStore.categoryEdit', context: context);

  @override
  bool get categoryEdit {
    _$categoryEditAtom.reportRead();
    return super.categoryEdit;
  }

  @override
  set categoryEdit(bool value) {
    _$categoryEditAtom.reportWrite(value, super.categoryEdit, () {
      super.categoryEdit = value;
    });
  }

  late final _$categoryDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.categoryDelete', context: context);

  @override
  bool get categoryDelete {
    _$categoryDeleteAtom.reportRead();
    return super.categoryDelete;
  }

  @override
  set categoryDelete(bool value) {
    _$categoryDeleteAtom.reportWrite(value, super.categoryDelete, () {
      super.categoryDelete = value;
    });
  }

  late final _$serviceAtom =
      Atom(name: '_RolesAndPermissionStore.service', context: context);

  @override
  bool get service {
    _$serviceAtom.reportRead();
    return super.service;
  }

  @override
  set service(bool value) {
    _$serviceAtom.reportWrite(value, super.service, () {
      super.service = value;
    });
  }

  late final _$serviceAddAtom =
      Atom(name: '_RolesAndPermissionStore.serviceAdd', context: context);

  @override
  bool get serviceAdd {
    _$serviceAddAtom.reportRead();
    return super.serviceAdd;
  }

  @override
  set serviceAdd(bool value) {
    _$serviceAddAtom.reportWrite(value, super.serviceAdd, () {
      super.serviceAdd = value;
    });
  }

  late final _$serviceListAtom =
      Atom(name: '_RolesAndPermissionStore.serviceList', context: context);

  @override
  bool get serviceList {
    _$serviceListAtom.reportRead();
    return super.serviceList;
  }

  @override
  set serviceList(bool value) {
    _$serviceListAtom.reportWrite(value, super.serviceList, () {
      super.serviceList = value;
    });
  }

  late final _$serviceEditAtom =
      Atom(name: '_RolesAndPermissionStore.serviceEdit', context: context);

  @override
  bool get serviceEdit {
    _$serviceEditAtom.reportRead();
    return super.serviceEdit;
  }

  @override
  set serviceEdit(bool value) {
    _$serviceEditAtom.reportWrite(value, super.serviceEdit, () {
      super.serviceEdit = value;
    });
  }

  late final _$serviceDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.serviceDelete', context: context);

  @override
  bool get serviceDelete {
    _$serviceDeleteAtom.reportRead();
    return super.serviceDelete;
  }

  @override
  set serviceDelete(bool value) {
    _$serviceDeleteAtom.reportWrite(value, super.serviceDelete, () {
      super.serviceDelete = value;
    });
  }

  late final _$providerAtom =
      Atom(name: '_RolesAndPermissionStore.provider', context: context);

  @override
  bool get provider {
    _$providerAtom.reportRead();
    return super.provider;
  }

  @override
  set provider(bool value) {
    _$providerAtom.reportWrite(value, super.provider, () {
      super.provider = value;
    });
  }

  late final _$providerAddAtom =
      Atom(name: '_RolesAndPermissionStore.providerAdd', context: context);

  @override
  bool get providerAdd {
    _$providerAddAtom.reportRead();
    return super.providerAdd;
  }

  @override
  set providerAdd(bool value) {
    _$providerAddAtom.reportWrite(value, super.providerAdd, () {
      super.providerAdd = value;
    });
  }

  late final _$providerListAtom =
      Atom(name: '_RolesAndPermissionStore.providerList', context: context);

  @override
  bool get providerList {
    _$providerListAtom.reportRead();
    return super.providerList;
  }

  @override
  set providerList(bool value) {
    _$providerListAtom.reportWrite(value, super.providerList, () {
      super.providerList = value;
    });
  }

  late final _$providerEditAtom =
      Atom(name: '_RolesAndPermissionStore.providerEdit', context: context);

  @override
  bool get providerEdit {
    _$providerEditAtom.reportRead();
    return super.providerEdit;
  }

  @override
  set providerEdit(bool value) {
    _$providerEditAtom.reportWrite(value, super.providerEdit, () {
      super.providerEdit = value;
    });
  }

  late final _$providerDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.providerDelete', context: context);

  @override
  bool get providerDelete {
    _$providerDeleteAtom.reportRead();
    return super.providerDelete;
  }

  @override
  set providerDelete(bool value) {
    _$providerDeleteAtom.reportWrite(value, super.providerDelete, () {
      super.providerDelete = value;
    });
  }

  late final _$handymanAtom =
      Atom(name: '_RolesAndPermissionStore.handyman', context: context);

  @override
  bool get handyman {
    _$handymanAtom.reportRead();
    return super.handyman;
  }

  @override
  set handyman(bool value) {
    _$handymanAtom.reportWrite(value, super.handyman, () {
      super.handyman = value;
    });
  }

  late final _$handymanListAtom =
      Atom(name: '_RolesAndPermissionStore.handymanList', context: context);

  @override
  bool get handymanList {
    _$handymanListAtom.reportRead();
    return super.handymanList;
  }

  @override
  set handymanList(bool value) {
    _$handymanListAtom.reportWrite(value, super.handymanList, () {
      super.handymanList = value;
    });
  }

  late final _$handymanAddAtom =
      Atom(name: '_RolesAndPermissionStore.handymanAdd', context: context);

  @override
  bool get handymanAdd {
    _$handymanAddAtom.reportRead();
    return super.handymanAdd;
  }

  @override
  set handymanAdd(bool value) {
    _$handymanAddAtom.reportWrite(value, super.handymanAdd, () {
      super.handymanAdd = value;
    });
  }

  late final _$handymanEditAtom =
      Atom(name: '_RolesAndPermissionStore.handymanEdit', context: context);

  @override
  bool get handymanEdit {
    _$handymanEditAtom.reportRead();
    return super.handymanEdit;
  }

  @override
  set handymanEdit(bool value) {
    _$handymanEditAtom.reportWrite(value, super.handymanEdit, () {
      super.handymanEdit = value;
    });
  }

  late final _$handymanDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.handymanDelete', context: context);

  @override
  bool get handymanDelete {
    _$handymanDeleteAtom.reportRead();
    return super.handymanDelete;
  }

  @override
  set handymanDelete(bool value) {
    _$handymanDeleteAtom.reportWrite(value, super.handymanDelete, () {
      super.handymanDelete = value;
    });
  }

  late final _$bookingAtom =
      Atom(name: '_RolesAndPermissionStore.booking', context: context);

  @override
  bool get booking {
    _$bookingAtom.reportRead();
    return super.booking;
  }

  @override
  set booking(bool value) {
    _$bookingAtom.reportWrite(value, super.booking, () {
      super.booking = value;
    });
  }

  late final _$bookingListAtom =
      Atom(name: '_RolesAndPermissionStore.bookingList', context: context);

  @override
  bool get bookingList {
    _$bookingListAtom.reportRead();
    return super.bookingList;
  }

  @override
  set bookingList(bool value) {
    _$bookingListAtom.reportWrite(value, super.bookingList, () {
      super.bookingList = value;
    });
  }

  late final _$bookingEditAtom =
      Atom(name: '_RolesAndPermissionStore.bookingEdit', context: context);

  @override
  bool get bookingEdit {
    _$bookingEditAtom.reportRead();
    return super.bookingEdit;
  }

  @override
  set bookingEdit(bool value) {
    _$bookingEditAtom.reportWrite(value, super.bookingEdit, () {
      super.bookingEdit = value;
    });
  }

  late final _$bookingDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.bookingDelete', context: context);

  @override
  bool get bookingDelete {
    _$bookingDeleteAtom.reportRead();
    return super.bookingDelete;
  }

  @override
  set bookingDelete(bool value) {
    _$bookingDeleteAtom.reportWrite(value, super.bookingDelete, () {
      super.bookingDelete = value;
    });
  }

  late final _$bookingViewAtom =
      Atom(name: '_RolesAndPermissionStore.bookingView', context: context);

  @override
  bool get bookingView {
    _$bookingViewAtom.reportRead();
    return super.bookingView;
  }

  @override
  set bookingView(bool value) {
    _$bookingViewAtom.reportWrite(value, super.bookingView, () {
      super.bookingView = value;
    });
  }

  late final _$paymentAtom =
      Atom(name: '_RolesAndPermissionStore.payment', context: context);

  @override
  bool get payment {
    _$paymentAtom.reportRead();
    return super.payment;
  }

  @override
  set payment(bool value) {
    _$paymentAtom.reportWrite(value, super.payment, () {
      super.payment = value;
    });
  }

  late final _$paymentListAtom =
      Atom(name: '_RolesAndPermissionStore.paymentList', context: context);

  @override
  bool get paymentList {
    _$paymentListAtom.reportRead();
    return super.paymentList;
  }

  @override
  set paymentList(bool value) {
    _$paymentListAtom.reportWrite(value, super.paymentList, () {
      super.paymentList = value;
    });
  }

  late final _$userAtom =
      Atom(name: '_RolesAndPermissionStore.user', context: context);

  @override
  bool get user {
    _$userAtom.reportRead();
    return super.user;
  }

  @override
  set user(bool value) {
    _$userAtom.reportWrite(value, super.user, () {
      super.user = value;
    });
  }

  late final _$userListAtom =
      Atom(name: '_RolesAndPermissionStore.userList', context: context);

  @override
  bool get userList {
    _$userListAtom.reportRead();
    return super.userList;
  }

  @override
  set userList(bool value) {
    _$userListAtom.reportWrite(value, super.userList, () {
      super.userList = value;
    });
  }

  late final _$userViewAtom =
      Atom(name: '_RolesAndPermissionStore.userView', context: context);

  @override
  bool get userView {
    _$userViewAtom.reportRead();
    return super.userView;
  }

  @override
  set userView(bool value) {
    _$userViewAtom.reportWrite(value, super.userView, () {
      super.userView = value;
    });
  }

  late final _$userDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.userDelete', context: context);

  @override
  bool get userDelete {
    _$userDeleteAtom.reportRead();
    return super.userDelete;
  }

  @override
  set userDelete(bool value) {
    _$userDeleteAtom.reportWrite(value, super.userDelete, () {
      super.userDelete = value;
    });
  }

  late final _$providerTypeAtom =
      Atom(name: '_RolesAndPermissionStore.providerType', context: context);

  @override
  bool get providerType {
    _$providerTypeAtom.reportRead();
    return super.providerType;
  }

  @override
  set providerType(bool value) {
    _$providerTypeAtom.reportWrite(value, super.providerType, () {
      super.providerType = value;
    });
  }

  late final _$providerTypeListAtom =
      Atom(name: '_RolesAndPermissionStore.providerTypeList', context: context);

  @override
  bool get providerTypeList {
    _$providerTypeListAtom.reportRead();
    return super.providerTypeList;
  }

  @override
  set providerTypeList(bool value) {
    _$providerTypeListAtom.reportWrite(value, super.providerTypeList, () {
      super.providerTypeList = value;
    });
  }

  late final _$providerTypeAddAtom =
      Atom(name: '_RolesAndPermissionStore.providerTypeAdd', context: context);

  @override
  bool get providerTypeAdd {
    _$providerTypeAddAtom.reportRead();
    return super.providerTypeAdd;
  }

  @override
  set providerTypeAdd(bool value) {
    _$providerTypeAddAtom.reportWrite(value, super.providerTypeAdd, () {
      super.providerTypeAdd = value;
    });
  }

  late final _$providerTypeEditAtom =
      Atom(name: '_RolesAndPermissionStore.providerTypeEdit', context: context);

  @override
  bool get providerTypeEdit {
    _$providerTypeEditAtom.reportRead();
    return super.providerTypeEdit;
  }

  @override
  set providerTypeEdit(bool value) {
    _$providerTypeEditAtom.reportWrite(value, super.providerTypeEdit, () {
      super.providerTypeEdit = value;
    });
  }

  late final _$providerTypeDeleteAtom = Atom(
      name: '_RolesAndPermissionStore.providerTypeDelete', context: context);

  @override
  bool get providerTypeDelete {
    _$providerTypeDeleteAtom.reportRead();
    return super.providerTypeDelete;
  }

  @override
  set providerTypeDelete(bool value) {
    _$providerTypeDeleteAtom.reportWrite(value, super.providerTypeDelete, () {
      super.providerTypeDelete = value;
    });
  }

  late final _$couponAtom =
      Atom(name: '_RolesAndPermissionStore.coupon', context: context);

  @override
  bool get coupon {
    _$couponAtom.reportRead();
    return super.coupon;
  }

  @override
  set coupon(bool value) {
    _$couponAtom.reportWrite(value, super.coupon, () {
      super.coupon = value;
    });
  }

  late final _$couponListAtom =
      Atom(name: '_RolesAndPermissionStore.couponList', context: context);

  @override
  bool get couponList {
    _$couponListAtom.reportRead();
    return super.couponList;
  }

  @override
  set couponList(bool value) {
    _$couponListAtom.reportWrite(value, super.couponList, () {
      super.couponList = value;
    });
  }

  late final _$couponAddAtom =
      Atom(name: '_RolesAndPermissionStore.couponAdd', context: context);

  @override
  bool get couponAdd {
    _$couponAddAtom.reportRead();
    return super.couponAdd;
  }

  @override
  set couponAdd(bool value) {
    _$couponAddAtom.reportWrite(value, super.couponAdd, () {
      super.couponAdd = value;
    });
  }

  late final _$couponEditAtom =
      Atom(name: '_RolesAndPermissionStore.couponEdit', context: context);

  @override
  bool get couponEdit {
    _$couponEditAtom.reportRead();
    return super.couponEdit;
  }

  @override
  set couponEdit(bool value) {
    _$couponEditAtom.reportWrite(value, super.couponEdit, () {
      super.couponEdit = value;
    });
  }

  late final _$couponDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.couponDelete', context: context);

  @override
  bool get couponDelete {
    _$couponDeleteAtom.reportRead();
    return super.couponDelete;
  }

  @override
  set couponDelete(bool value) {
    _$couponDeleteAtom.reportWrite(value, super.couponDelete, () {
      super.couponDelete = value;
    });
  }

  late final _$sliderAtom =
      Atom(name: '_RolesAndPermissionStore.slider', context: context);

  @override
  bool get slider {
    _$sliderAtom.reportRead();
    return super.slider;
  }

  @override
  set slider(bool value) {
    _$sliderAtom.reportWrite(value, super.slider, () {
      super.slider = value;
    });
  }

  late final _$sliderListAtom =
      Atom(name: '_RolesAndPermissionStore.sliderList', context: context);

  @override
  bool get sliderList {
    _$sliderListAtom.reportRead();
    return super.sliderList;
  }

  @override
  set sliderList(bool value) {
    _$sliderListAtom.reportWrite(value, super.sliderList, () {
      super.sliderList = value;
    });
  }

  late final _$sliderAddAtom =
      Atom(name: '_RolesAndPermissionStore.sliderAdd', context: context);

  @override
  bool get sliderAdd {
    _$sliderAddAtom.reportRead();
    return super.sliderAdd;
  }

  @override
  set sliderAdd(bool value) {
    _$sliderAddAtom.reportWrite(value, super.sliderAdd, () {
      super.sliderAdd = value;
    });
  }

  late final _$sliderEditAtom =
      Atom(name: '_RolesAndPermissionStore.sliderEdit', context: context);

  @override
  bool get sliderEdit {
    _$sliderEditAtom.reportRead();
    return super.sliderEdit;
  }

  @override
  set sliderEdit(bool value) {
    _$sliderEditAtom.reportWrite(value, super.sliderEdit, () {
      super.sliderEdit = value;
    });
  }

  late final _$sliderDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.sliderDelete', context: context);

  @override
  bool get sliderDelete {
    _$sliderDeleteAtom.reportRead();
    return super.sliderDelete;
  }

  @override
  set sliderDelete(bool value) {
    _$sliderDeleteAtom.reportWrite(value, super.sliderDelete, () {
      super.sliderDelete = value;
    });
  }

  late final _$providerAddressAtom =
      Atom(name: '_RolesAndPermissionStore.providerAddress', context: context);

  @override
  bool get providerAddress {
    _$providerAddressAtom.reportRead();
    return super.providerAddress;
  }

  @override
  set providerAddress(bool value) {
    _$providerAddressAtom.reportWrite(value, super.providerAddress, () {
      super.providerAddress = value;
    });
  }

  late final _$providerAddressListAtom = Atom(
      name: '_RolesAndPermissionStore.providerAddressList', context: context);

  @override
  bool get providerAddressList {
    _$providerAddressListAtom.reportRead();
    return super.providerAddressList;
  }

  @override
  set providerAddressList(bool value) {
    _$providerAddressListAtom.reportWrite(value, super.providerAddressList, () {
      super.providerAddressList = value;
    });
  }

  late final _$providerAddressAddAtom = Atom(
      name: '_RolesAndPermissionStore.providerAddressAdd', context: context);

  @override
  bool get providerAddressAdd {
    _$providerAddressAddAtom.reportRead();
    return super.providerAddressAdd;
  }

  @override
  set providerAddressAdd(bool value) {
    _$providerAddressAddAtom.reportWrite(value, super.providerAddressAdd, () {
      super.providerAddressAdd = value;
    });
  }

  late final _$providerAddressEditAtom = Atom(
      name: '_RolesAndPermissionStore.providerAddressEdit', context: context);

  @override
  bool get providerAddressEdit {
    _$providerAddressEditAtom.reportRead();
    return super.providerAddressEdit;
  }

  @override
  set providerAddressEdit(bool value) {
    _$providerAddressEditAtom.reportWrite(value, super.providerAddressEdit, () {
      super.providerAddressEdit = value;
    });
  }

  late final _$providerAddressDeleteAtom = Atom(
      name: '_RolesAndPermissionStore.providerAddressDelete', context: context);

  @override
  bool get providerAddressDelete {
    _$providerAddressDeleteAtom.reportRead();
    return super.providerAddressDelete;
  }

  @override
  set providerAddressDelete(bool value) {
    _$providerAddressDeleteAtom.reportWrite(value, super.providerAddressDelete,
        () {
      super.providerAddressDelete = value;
    });
  }

  late final _$documentAtom =
      Atom(name: '_RolesAndPermissionStore.document', context: context);

  @override
  bool get document {
    _$documentAtom.reportRead();
    return super.document;
  }

  @override
  set document(bool value) {
    _$documentAtom.reportWrite(value, super.document, () {
      super.document = value;
    });
  }

  late final _$documentListAtom =
      Atom(name: '_RolesAndPermissionStore.documentList', context: context);

  @override
  bool get documentList {
    _$documentListAtom.reportRead();
    return super.documentList;
  }

  @override
  set documentList(bool value) {
    _$documentListAtom.reportWrite(value, super.documentList, () {
      super.documentList = value;
    });
  }

  late final _$documentAddAtom =
      Atom(name: '_RolesAndPermissionStore.documentAdd', context: context);

  @override
  bool get documentAdd {
    _$documentAddAtom.reportRead();
    return super.documentAdd;
  }

  @override
  set documentAdd(bool value) {
    _$documentAddAtom.reportWrite(value, super.documentAdd, () {
      super.documentAdd = value;
    });
  }

  late final _$documentEditAtom =
      Atom(name: '_RolesAndPermissionStore.documentEdit', context: context);

  @override
  bool get documentEdit {
    _$documentEditAtom.reportRead();
    return super.documentEdit;
  }

  @override
  set documentEdit(bool value) {
    _$documentEditAtom.reportWrite(value, super.documentEdit, () {
      super.documentEdit = value;
    });
  }

  late final _$documentDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.documentDelete', context: context);

  @override
  bool get documentDelete {
    _$documentDeleteAtom.reportRead();
    return super.documentDelete;
  }

  @override
  set documentDelete(bool value) {
    _$documentDeleteAtom.reportWrite(value, super.documentDelete, () {
      super.documentDelete = value;
    });
  }

  late final _$handymanPayoutAtom =
      Atom(name: '_RolesAndPermissionStore.handymanPayout', context: context);

  @override
  bool get handymanPayout {
    _$handymanPayoutAtom.reportRead();
    return super.handymanPayout;
  }

  @override
  set handymanPayout(bool value) {
    _$handymanPayoutAtom.reportWrite(value, super.handymanPayout, () {
      super.handymanPayout = value;
    });
  }

  late final _$serviceFAQAtom =
      Atom(name: '_RolesAndPermissionStore.serviceFAQ', context: context);

  @override
  bool get serviceFAQ {
    _$serviceFAQAtom.reportRead();
    return super.serviceFAQ;
  }

  @override
  set serviceFAQ(bool value) {
    _$serviceFAQAtom.reportWrite(value, super.serviceFAQ, () {
      super.serviceFAQ = value;
    });
  }

  late final _$serviceFAQAddAtom =
      Atom(name: '_RolesAndPermissionStore.serviceFAQAdd', context: context);

  @override
  bool get serviceFAQAdd {
    _$serviceFAQAddAtom.reportRead();
    return super.serviceFAQAdd;
  }

  @override
  set serviceFAQAdd(bool value) {
    _$serviceFAQAddAtom.reportWrite(value, super.serviceFAQAdd, () {
      super.serviceFAQAdd = value;
    });
  }

  late final _$serviceFAQEditAtom =
      Atom(name: '_RolesAndPermissionStore.serviceFAQEdit', context: context);

  @override
  bool get serviceFAQEdit {
    _$serviceFAQEditAtom.reportRead();
    return super.serviceFAQEdit;
  }

  @override
  set serviceFAQEdit(bool value) {
    _$serviceFAQEditAtom.reportWrite(value, super.serviceFAQEdit, () {
      super.serviceFAQEdit = value;
    });
  }

  late final _$serviceFAQDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.serviceFAQDelete', context: context);

  @override
  bool get serviceFAQDelete {
    _$serviceFAQDeleteAtom.reportRead();
    return super.serviceFAQDelete;
  }

  @override
  set serviceFAQDelete(bool value) {
    _$serviceFAQDeleteAtom.reportWrite(value, super.serviceFAQDelete, () {
      super.serviceFAQDelete = value;
    });
  }

  late final _$serviceFAQListAtom =
      Atom(name: '_RolesAndPermissionStore.serviceFAQList', context: context);

  @override
  bool get serviceFAQList {
    _$serviceFAQListAtom.reportRead();
    return super.serviceFAQList;
  }

  @override
  set serviceFAQList(bool value) {
    _$serviceFAQListAtom.reportWrite(value, super.serviceFAQList, () {
      super.serviceFAQList = value;
    });
  }

  late final _$userAddAtom =
      Atom(name: '_RolesAndPermissionStore.userAdd', context: context);

  @override
  bool get userAdd {
    _$userAddAtom.reportRead();
    return super.userAdd;
  }

  @override
  set userAdd(bool value) {
    _$userAddAtom.reportWrite(value, super.userAdd, () {
      super.userAdd = value;
    });
  }

  late final _$userEditAtom =
      Atom(name: '_RolesAndPermissionStore.userEdit', context: context);

  @override
  bool get userEdit {
    _$userEditAtom.reportRead();
    return super.userEdit;
  }

  @override
  set userEdit(bool value) {
    _$userEditAtom.reportWrite(value, super.userEdit, () {
      super.userEdit = value;
    });
  }

  late final _$subCategoryAtom =
      Atom(name: '_RolesAndPermissionStore.subCategory', context: context);

  @override
  bool get subCategory {
    _$subCategoryAtom.reportRead();
    return super.subCategory;
  }

  @override
  set subCategory(bool value) {
    _$subCategoryAtom.reportWrite(value, super.subCategory, () {
      super.subCategory = value;
    });
  }

  late final _$subCategoryAddAtom =
      Atom(name: '_RolesAndPermissionStore.subCategoryAdd', context: context);

  @override
  bool get subCategoryAdd {
    _$subCategoryAddAtom.reportRead();
    return super.subCategoryAdd;
  }

  @override
  set subCategoryAdd(bool value) {
    _$subCategoryAddAtom.reportWrite(value, super.subCategoryAdd, () {
      super.subCategoryAdd = value;
    });
  }

  late final _$subCategoryEditAtom =
      Atom(name: '_RolesAndPermissionStore.subCategoryEdit', context: context);

  @override
  bool get subCategoryEdit {
    _$subCategoryEditAtom.reportRead();
    return super.subCategoryEdit;
  }

  @override
  set subCategoryEdit(bool value) {
    _$subCategoryEditAtom.reportWrite(value, super.subCategoryEdit, () {
      super.subCategoryEdit = value;
    });
  }

  late final _$subCategoryDeleteAtom = Atom(
      name: '_RolesAndPermissionStore.subCategoryDelete', context: context);

  @override
  bool get subCategoryDelete {
    _$subCategoryDeleteAtom.reportRead();
    return super.subCategoryDelete;
  }

  @override
  set subCategoryDelete(bool value) {
    _$subCategoryDeleteAtom.reportWrite(value, super.subCategoryDelete, () {
      super.subCategoryDelete = value;
    });
  }

  late final _$subCategoryListAtom =
      Atom(name: '_RolesAndPermissionStore.subCategoryList', context: context);

  @override
  bool get subCategoryList {
    _$subCategoryListAtom.reportRead();
    return super.subCategoryList;
  }

  @override
  set subCategoryList(bool value) {
    _$subCategoryListAtom.reportWrite(value, super.subCategoryList, () {
      super.subCategoryList = value;
    });
  }

  late final _$handymanTypeAtom =
      Atom(name: '_RolesAndPermissionStore.handymanType', context: context);

  @override
  bool get handymanType {
    _$handymanTypeAtom.reportRead();
    return super.handymanType;
  }

  @override
  set handymanType(bool value) {
    _$handymanTypeAtom.reportWrite(value, super.handymanType, () {
      super.handymanType = value;
    });
  }

  late final _$handymanTypeListAtom =
      Atom(name: '_RolesAndPermissionStore.handymanTypeList', context: context);

  @override
  bool get handymanTypeList {
    _$handymanTypeListAtom.reportRead();
    return super.handymanTypeList;
  }

  @override
  set handymanTypeList(bool value) {
    _$handymanTypeListAtom.reportWrite(value, super.handymanTypeList, () {
      super.handymanTypeList = value;
    });
  }

  late final _$handymanTypeAddAtom =
      Atom(name: '_RolesAndPermissionStore.handymanTypeAdd', context: context);

  @override
  bool get handymanTypeAdd {
    _$handymanTypeAddAtom.reportRead();
    return super.handymanTypeAdd;
  }

  @override
  set handymanTypeAdd(bool value) {
    _$handymanTypeAddAtom.reportWrite(value, super.handymanTypeAdd, () {
      super.handymanTypeAdd = value;
    });
  }

  late final _$handymanTypeEditAtom =
      Atom(name: '_RolesAndPermissionStore.handymanTypeEdit', context: context);

  @override
  bool get handymanTypeEdit {
    _$handymanTypeEditAtom.reportRead();
    return super.handymanTypeEdit;
  }

  @override
  set handymanTypeEdit(bool value) {
    _$handymanTypeEditAtom.reportWrite(value, super.handymanTypeEdit, () {
      super.handymanTypeEdit = value;
    });
  }

  late final _$handymanTypeDeleteAtom = Atom(
      name: '_RolesAndPermissionStore.handymanTypeDelete', context: context);

  @override
  bool get handymanTypeDelete {
    _$handymanTypeDeleteAtom.reportRead();
    return super.handymanTypeDelete;
  }

  @override
  set handymanTypeDelete(bool value) {
    _$handymanTypeDeleteAtom.reportWrite(value, super.handymanTypeDelete, () {
      super.handymanTypeDelete = value;
    });
  }

  late final _$postJobAtom =
      Atom(name: '_RolesAndPermissionStore.postJob', context: context);

  @override
  bool get postJob {
    _$postJobAtom.reportRead();
    return super.postJob;
  }

  @override
  set postJob(bool value) {
    _$postJobAtom.reportWrite(value, super.postJob, () {
      super.postJob = value;
    });
  }

  late final _$postJobListAtom =
      Atom(name: '_RolesAndPermissionStore.postJobList', context: context);

  @override
  bool get postJobList {
    _$postJobListAtom.reportRead();
    return super.postJobList;
  }

  @override
  set postJobList(bool value) {
    _$postJobListAtom.reportWrite(value, super.postJobList, () {
      super.postJobList = value;
    });
  }

  late final _$servicePackageAtom =
      Atom(name: '_RolesAndPermissionStore.servicePackage', context: context);

  @override
  bool get servicePackage {
    _$servicePackageAtom.reportRead();
    return super.servicePackage;
  }

  @override
  set servicePackage(bool value) {
    _$servicePackageAtom.reportWrite(value, super.servicePackage, () {
      super.servicePackage = value;
    });
  }

  late final _$servicePackageAddAtom = Atom(
      name: '_RolesAndPermissionStore.servicePackageAdd', context: context);

  @override
  bool get servicePackageAdd {
    _$servicePackageAddAtom.reportRead();
    return super.servicePackageAdd;
  }

  @override
  set servicePackageAdd(bool value) {
    _$servicePackageAddAtom.reportWrite(value, super.servicePackageAdd, () {
      super.servicePackageAdd = value;
    });
  }

  late final _$servicePackageEditAtom = Atom(
      name: '_RolesAndPermissionStore.servicePackageEdit', context: context);

  @override
  bool get servicePackageEdit {
    _$servicePackageEditAtom.reportRead();
    return super.servicePackageEdit;
  }

  @override
  set servicePackageEdit(bool value) {
    _$servicePackageEditAtom.reportWrite(value, super.servicePackageEdit, () {
      super.servicePackageEdit = value;
    });
  }

  late final _$servicePackageDeleteAtom = Atom(
      name: '_RolesAndPermissionStore.servicePackageDelete', context: context);

  @override
  bool get servicePackageDelete {
    _$servicePackageDeleteAtom.reportRead();
    return super.servicePackageDelete;
  }

  @override
  set servicePackageDelete(bool value) {
    _$servicePackageDeleteAtom.reportWrite(value, super.servicePackageDelete,
        () {
      super.servicePackageDelete = value;
    });
  }

  late final _$servicePackageListAtom = Atom(
      name: '_RolesAndPermissionStore.servicePackageList', context: context);

  @override
  bool get servicePackageList {
    _$servicePackageListAtom.reportRead();
    return super.servicePackageList;
  }

  @override
  set servicePackageList(bool value) {
    _$servicePackageListAtom.reportWrite(value, super.servicePackageList, () {
      super.servicePackageList = value;
    });
  }

  late final _$refundAndCancellationPolicyAtom = Atom(
      name: '_RolesAndPermissionStore.refundAndCancellationPolicy',
      context: context);

  @override
  bool get refundAndCancellationPolicy {
    _$refundAndCancellationPolicyAtom.reportRead();
    return super.refundAndCancellationPolicy;
  }

  @override
  set refundAndCancellationPolicy(bool value) {
    _$refundAndCancellationPolicyAtom
        .reportWrite(value, super.refundAndCancellationPolicy, () {
      super.refundAndCancellationPolicy = value;
    });
  }

  late final _$blogAtom =
      Atom(name: '_RolesAndPermissionStore.blog', context: context);

  @override
  bool get blog {
    _$blogAtom.reportRead();
    return super.blog;
  }

  @override
  set blog(bool value) {
    _$blogAtom.reportWrite(value, super.blog, () {
      super.blog = value;
    });
  }

  late final _$blogAddAtom =
      Atom(name: '_RolesAndPermissionStore.blogAdd', context: context);

  @override
  bool get blogAdd {
    _$blogAddAtom.reportRead();
    return super.blogAdd;
  }

  @override
  set blogAdd(bool value) {
    _$blogAddAtom.reportWrite(value, super.blogAdd, () {
      super.blogAdd = value;
    });
  }

  late final _$blogEditAtom =
      Atom(name: '_RolesAndPermissionStore.blogEdit', context: context);

  @override
  bool get blogEdit {
    _$blogEditAtom.reportRead();
    return super.blogEdit;
  }

  @override
  set blogEdit(bool value) {
    _$blogEditAtom.reportWrite(value, super.blogEdit, () {
      super.blogEdit = value;
    });
  }

  late final _$blogDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.blogDelete', context: context);

  @override
  bool get blogDelete {
    _$blogDeleteAtom.reportRead();
    return super.blogDelete;
  }

  @override
  set blogDelete(bool value) {
    _$blogDeleteAtom.reportWrite(value, super.blogDelete, () {
      super.blogDelete = value;
    });
  }

  late final _$blogListAtom =
      Atom(name: '_RolesAndPermissionStore.blogList', context: context);

  @override
  bool get blogList {
    _$blogListAtom.reportRead();
    return super.blogList;
  }

  @override
  set blogList(bool value) {
    _$blogListAtom.reportWrite(value, super.blogList, () {
      super.blogList = value;
    });
  }

  late final _$serviceAddOnAtom =
      Atom(name: '_RolesAndPermissionStore.serviceAddOn', context: context);

  @override
  bool get serviceAddOn {
    _$serviceAddOnAtom.reportRead();
    return super.serviceAddOn;
  }

  @override
  set serviceAddOn(bool value) {
    _$serviceAddOnAtom.reportWrite(value, super.serviceAddOn, () {
      super.serviceAddOn = value;
    });
  }

  late final _$serviceAddOnAddAtom =
      Atom(name: '_RolesAndPermissionStore.serviceAddOnAdd', context: context);

  @override
  bool get serviceAddOnAdd {
    _$serviceAddOnAddAtom.reportRead();
    return super.serviceAddOnAdd;
  }

  @override
  set serviceAddOnAdd(bool value) {
    _$serviceAddOnAddAtom.reportWrite(value, super.serviceAddOnAdd, () {
      super.serviceAddOnAdd = value;
    });
  }

  late final _$serviceAddOnEditAtom =
      Atom(name: '_RolesAndPermissionStore.serviceAddOnEdit', context: context);

  @override
  bool get serviceAddOnEdit {
    _$serviceAddOnEditAtom.reportRead();
    return super.serviceAddOnEdit;
  }

  @override
  set serviceAddOnEdit(bool value) {
    _$serviceAddOnEditAtom.reportWrite(value, super.serviceAddOnEdit, () {
      super.serviceAddOnEdit = value;
    });
  }

  late final _$serviceAddOnListAtom =
      Atom(name: '_RolesAndPermissionStore.serviceAddOnList', context: context);

  @override
  bool get serviceAddOnList {
    _$serviceAddOnListAtom.reportRead();
    return super.serviceAddOnList;
  }

  @override
  set serviceAddOnList(bool value) {
    _$serviceAddOnListAtom.reportWrite(value, super.serviceAddOnList, () {
      super.serviceAddOnList = value;
    });
  }

  late final _$frontendSettingAtom =
      Atom(name: '_RolesAndPermissionStore.frontendSetting', context: context);

  @override
  bool get frontendSetting {
    _$frontendSettingAtom.reportRead();
    return super.frontendSetting;
  }

  @override
  set frontendSetting(bool value) {
    _$frontendSettingAtom.reportWrite(value, super.frontendSetting, () {
      super.frontendSetting = value;
    });
  }

  late final _$frontendSettingListAtom = Atom(
      name: '_RolesAndPermissionStore.frontendSettingList', context: context);

  @override
  bool get frontendSettingList {
    _$frontendSettingListAtom.reportRead();
    return super.frontendSettingList;
  }

  @override
  set frontendSettingList(bool value) {
    _$frontendSettingListAtom.reportWrite(value, super.frontendSettingList, () {
      super.frontendSettingList = value;
    });
  }

  late final _$bankAtom =
      Atom(name: '_RolesAndPermissionStore.bank', context: context);

  @override
  bool get bank {
    _$bankAtom.reportRead();
    return super.bank;
  }

  @override
  set bank(bool value) {
    _$bankAtom.reportWrite(value, super.bank, () {
      super.bank = value;
    });
  }

  late final _$bankAddAtom =
      Atom(name: '_RolesAndPermissionStore.bankAdd', context: context);

  @override
  bool get bankAdd {
    _$bankAddAtom.reportRead();
    return super.bankAdd;
  }

  @override
  set bankAdd(bool value) {
    _$bankAddAtom.reportWrite(value, super.bankAdd, () {
      super.bankAdd = value;
    });
  }

  late final _$bankEditAtom =
      Atom(name: '_RolesAndPermissionStore.bankEdit', context: context);

  @override
  bool get bankEdit {
    _$bankEditAtom.reportRead();
    return super.bankEdit;
  }

  @override
  set bankEdit(bool value) {
    _$bankEditAtom.reportWrite(value, super.bankEdit, () {
      super.bankEdit = value;
    });
  }

  late final _$bankDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.bankDelete', context: context);

  @override
  bool get bankDelete {
    _$bankDeleteAtom.reportRead();
    return super.bankDelete;
  }

  @override
  set bankDelete(bool value) {
    _$bankDeleteAtom.reportWrite(value, super.bankDelete, () {
      super.bankDelete = value;
    });
  }

  late final _$bankListAtom =
      Atom(name: '_RolesAndPermissionStore.bankList', context: context);

  @override
  bool get bankList {
    _$bankListAtom.reportRead();
    return super.bankList;
  }

  @override
  set bankList(bool value) {
    _$bankListAtom.reportWrite(value, super.bankList, () {
      super.bankList = value;
    });
  }

  late final _$taxAtom =
      Atom(name: '_RolesAndPermissionStore.tax', context: context);

  @override
  bool get tax {
    _$taxAtom.reportRead();
    return super.tax;
  }

  @override
  set tax(bool value) {
    _$taxAtom.reportWrite(value, super.tax, () {
      super.tax = value;
    });
  }

  late final _$taxAddAtom =
      Atom(name: '_RolesAndPermissionStore.taxAdd', context: context);

  @override
  bool get taxAdd {
    _$taxAddAtom.reportRead();
    return super.taxAdd;
  }

  @override
  set taxAdd(bool value) {
    _$taxAddAtom.reportWrite(value, super.taxAdd, () {
      super.taxAdd = value;
    });
  }

  late final _$taxEditAtom =
      Atom(name: '_RolesAndPermissionStore.taxEdit', context: context);

  @override
  bool get taxEdit {
    _$taxEditAtom.reportRead();
    return super.taxEdit;
  }

  @override
  set taxEdit(bool value) {
    _$taxEditAtom.reportWrite(value, super.taxEdit, () {
      super.taxEdit = value;
    });
  }

  late final _$taxDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.taxDelete', context: context);

  @override
  bool get taxDelete {
    _$taxDeleteAtom.reportRead();
    return super.taxDelete;
  }

  @override
  set taxDelete(bool value) {
    _$taxDeleteAtom.reportWrite(value, super.taxDelete, () {
      super.taxDelete = value;
    });
  }

  late final _$taxListAtom =
      Atom(name: '_RolesAndPermissionStore.taxList', context: context);

  @override
  bool get taxList {
    _$taxListAtom.reportRead();
    return super.taxList;
  }

  @override
  set taxList(bool value) {
    _$taxListAtom.reportWrite(value, super.taxList, () {
      super.taxList = value;
    });
  }

  late final _$earningAtom =
      Atom(name: '_RolesAndPermissionStore.earning', context: context);

  @override
  bool get earning {
    _$earningAtom.reportRead();
    return super.earning;
  }

  @override
  set earning(bool value) {
    _$earningAtom.reportWrite(value, super.earning, () {
      super.earning = value;
    });
  }

  late final _$earningListAtom =
      Atom(name: '_RolesAndPermissionStore.earningList', context: context);

  @override
  bool get earningList {
    _$earningListAtom.reportRead();
    return super.earningList;
  }

  @override
  set earningList(bool value) {
    _$earningListAtom.reportWrite(value, super.earningList, () {
      super.earningList = value;
    });
  }

  late final _$walletAtom =
      Atom(name: '_RolesAndPermissionStore.wallet', context: context);

  @override
  bool get wallet {
    _$walletAtom.reportRead();
    return super.wallet;
  }

  @override
  set wallet(bool value) {
    _$walletAtom.reportWrite(value, super.wallet, () {
      super.wallet = value;
    });
  }

  late final _$walletAddAtom =
      Atom(name: '_RolesAndPermissionStore.walletAdd', context: context);

  @override
  bool get walletAdd {
    _$walletAddAtom.reportRead();
    return super.walletAdd;
  }

  @override
  set walletAdd(bool value) {
    _$walletAddAtom.reportWrite(value, super.walletAdd, () {
      super.walletAdd = value;
    });
  }

  late final _$walletEditAtom =
      Atom(name: '_RolesAndPermissionStore.walletEdit', context: context);

  @override
  bool get walletEdit {
    _$walletEditAtom.reportRead();
    return super.walletEdit;
  }

  @override
  set walletEdit(bool value) {
    _$walletEditAtom.reportWrite(value, super.walletEdit, () {
      super.walletEdit = value;
    });
  }

  late final _$walletDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.walletDelete', context: context);

  @override
  bool get walletDelete {
    _$walletDeleteAtom.reportRead();
    return super.walletDelete;
  }

  @override
  set walletDelete(bool value) {
    _$walletDeleteAtom.reportWrite(value, super.walletDelete, () {
      super.walletDelete = value;
    });
  }

  late final _$walletListAtom =
      Atom(name: '_RolesAndPermissionStore.walletList', context: context);

  @override
  bool get walletList {
    _$walletListAtom.reportRead();
    return super.walletList;
  }

  @override
  set walletList(bool value) {
    _$walletListAtom.reportWrite(value, super.walletList, () {
      super.walletList = value;
    });
  }

  late final _$userRatingAtom =
      Atom(name: '_RolesAndPermissionStore.userRating', context: context);

  @override
  bool get userRating {
    _$userRatingAtom.reportRead();
    return super.userRating;
  }

  @override
  set userRating(bool value) {
    _$userRatingAtom.reportWrite(value, super.userRating, () {
      super.userRating = value;
    });
  }

  late final _$userRatingListAtom =
      Atom(name: '_RolesAndPermissionStore.userRatingList', context: context);

  @override
  bool get userRatingList {
    _$userRatingListAtom.reportRead();
    return super.userRatingList;
  }

  @override
  set userRatingList(bool value) {
    _$userRatingListAtom.reportWrite(value, super.userRatingList, () {
      super.userRatingList = value;
    });
  }

  late final _$handymanRatingAtom =
      Atom(name: '_RolesAndPermissionStore.handymanRating', context: context);

  @override
  bool get handymanRating {
    _$handymanRatingAtom.reportRead();
    return super.handymanRating;
  }

  @override
  set handymanRating(bool value) {
    _$handymanRatingAtom.reportWrite(value, super.handymanRating, () {
      super.handymanRating = value;
    });
  }

  late final _$handymanRatingListAtom = Atom(
      name: '_RolesAndPermissionStore.handymanRatingList', context: context);

  @override
  bool get handymanRatingList {
    _$handymanRatingListAtom.reportRead();
    return super.handymanRatingList;
  }

  @override
  set handymanRatingList(bool value) {
    _$handymanRatingListAtom.reportWrite(value, super.handymanRatingList, () {
      super.handymanRatingList = value;
    });
  }

  late final _$providerPayoutAtom =
      Atom(name: '_RolesAndPermissionStore.providerPayout', context: context);

  @override
  bool get providerPayout {
    _$providerPayoutAtom.reportRead();
    return super.providerPayout;
  }

  @override
  set providerPayout(bool value) {
    _$providerPayoutAtom.reportWrite(value, super.providerPayout, () {
      super.providerPayout = value;
    });
  }

  late final _$planAtom =
      Atom(name: '_RolesAndPermissionStore.plan', context: context);

  @override
  bool get plan {
    _$planAtom.reportRead();
    return super.plan;
  }

  @override
  set plan(bool value) {
    _$planAtom.reportWrite(value, super.plan, () {
      super.plan = value;
    });
  }

  late final _$planAddAtom =
      Atom(name: '_RolesAndPermissionStore.planAdd', context: context);

  @override
  bool get planAdd {
    _$planAddAtom.reportRead();
    return super.planAdd;
  }

  @override
  set planAdd(bool value) {
    _$planAddAtom.reportWrite(value, super.planAdd, () {
      super.planAdd = value;
    });
  }

  late final _$planEditAtom =
      Atom(name: '_RolesAndPermissionStore.planEdit', context: context);

  @override
  bool get planEdit {
    _$planEditAtom.reportRead();
    return super.planEdit;
  }

  @override
  set planEdit(bool value) {
    _$planEditAtom.reportWrite(value, super.planEdit, () {
      super.planEdit = value;
    });
  }

  late final _$planDeleteAtom =
      Atom(name: '_RolesAndPermissionStore.planDelete', context: context);

  @override
  bool get planDelete {
    _$planDeleteAtom.reportRead();
    return super.planDelete;
  }

  @override
  set planDelete(bool value) {
    _$planDeleteAtom.reportWrite(value, super.planDelete, () {
      super.planDelete = value;
    });
  }

  late final _$planListAtom =
      Atom(name: '_RolesAndPermissionStore.planList', context: context);

  @override
  bool get planList {
    _$planListAtom.reportRead();
    return super.planList;
  }

  @override
  set planList(bool value) {
    _$planListAtom.reportWrite(value, super.planList, () {
      super.planList = value;
    });
  }

  late final _$userServiceListAtom =
      Atom(name: '_RolesAndPermissionStore.userServiceList', context: context);

  @override
  bool get userServiceList {
    _$userServiceListAtom.reportRead();
    return super.userServiceList;
  }

  @override
  set userServiceList(bool value) {
    _$userServiceListAtom.reportWrite(value, super.userServiceList, () {
      super.userServiceList = value;
    });
  }

  late final _$systemSettingAtom =
      Atom(name: '_RolesAndPermissionStore.systemSetting', context: context);

  @override
  bool get systemSetting {
    _$systemSettingAtom.reportRead();
    return super.systemSetting;
  }

  @override
  set systemSetting(bool value) {
    _$systemSettingAtom.reportWrite(value, super.systemSetting, () {
      super.systemSetting = value;
    });
  }

  late final _$providerChangePasswordAtom = Atom(
      name: '_RolesAndPermissionStore.providerChangePassword',
      context: context);

  @override
  bool get providerChangePassword {
    _$providerChangePasswordAtom.reportRead();
    return super.providerChangePassword;
  }

  @override
  set providerChangePassword(bool value) {
    _$providerChangePasswordAtom
        .reportWrite(value, super.providerChangePassword, () {
      super.providerChangePassword = value;
    });
  }

  late final _$dataDeletionRequestAtom = Atom(
      name: '_RolesAndPermissionStore.dataDeletionRequest', context: context);

  @override
  bool get dataDeletionRequest {
    _$dataDeletionRequestAtom.reportRead();
    return super.dataDeletionRequest;
  }

  @override
  set dataDeletionRequest(bool value) {
    _$dataDeletionRequestAtom.reportWrite(value, super.dataDeletionRequest, () {
      super.dataDeletionRequest = value;
    });
  }

  late final _$helpDeskAtom =
      Atom(name: '_RolesAndPermissionStore.helpDesk', context: context);

  @override
  bool get helpDesk {
    _$helpDeskAtom.reportRead();
    return super.helpDesk;
  }

  @override
  set helpDesk(bool value) {
    _$helpDeskAtom.reportWrite(value, super.helpDesk, () {
      super.helpDesk = value;
    });
  }

  late final _$helpDeskAddAtom =
      Atom(name: '_RolesAndPermissionStore.helpDeskAdd', context: context);

  @override
  bool get helpDeskAdd {
    _$helpDeskAddAtom.reportRead();
    return super.helpDeskAdd;
  }

  @override
  set helpDeskAdd(bool value) {
    _$helpDeskAddAtom.reportWrite(value, super.helpDeskAdd, () {
      super.helpDeskAdd = value;
    });
  }

  late final _$helpDeskEditAtom =
      Atom(name: '_RolesAndPermissionStore.helpDeskEdit', context: context);

  @override
  bool get helpDeskEdit {
    _$helpDeskEditAtom.reportRead();
    return super.helpDeskEdit;
  }

  @override
  set helpDeskEdit(bool value) {
    _$helpDeskEditAtom.reportWrite(value, super.helpDeskEdit, () {
      super.helpDeskEdit = value;
    });
  }

  late final _$helpDeskListAtom =
      Atom(name: '_RolesAndPermissionStore.helpDeskList', context: context);

  @override
  bool get helpDeskList {
    _$helpDeskListAtom.reportRead();
    return super.helpDeskList;
  }

  @override
  set helpDeskList(bool value) {
    _$helpDeskListAtom.reportWrite(value, super.helpDeskList, () {
      super.helpDeskList = value;
    });
  }

  late final _$pendingProviderAtom =
      Atom(name: '_RolesAndPermissionStore.pendingProvider', context: context);

  @override
  bool get pendingProvider {
    _$pendingProviderAtom.reportRead();
    return super.pendingProvider;
  }

  @override
  set pendingProvider(bool value) {
    _$pendingProviderAtom.reportWrite(value, super.pendingProvider, () {
      super.pendingProvider = value;
    });
  }

  late final _$pendingHandymanAtom =
      Atom(name: '_RolesAndPermissionStore.pendingHandyman', context: context);

  @override
  bool get pendingHandyman {
    _$pendingHandymanAtom.reportRead();
    return super.pendingHandyman;
  }

  @override
  set pendingHandyman(bool value) {
    _$pendingHandymanAtom.reportWrite(value, super.pendingHandyman, () {
      super.pendingHandyman = value;
    });
  }

  late final _$pagesAtom =
      Atom(name: '_RolesAndPermissionStore.pages', context: context);

  @override
  bool get pages {
    _$pagesAtom.reportRead();
    return super.pages;
  }

  @override
  set pages(bool value) {
    _$pagesAtom.reportWrite(value, super.pages, () {
      super.pages = value;
    });
  }

  late final _$aboutUsAtom =
      Atom(name: '_RolesAndPermissionStore.aboutUs', context: context);

  @override
  bool get aboutUs {
    _$aboutUsAtom.reportRead();
    return super.aboutUs;
  }

  @override
  set aboutUs(bool value) {
    _$aboutUsAtom.reportWrite(value, super.aboutUs, () {
      super.aboutUs = value;
    });
  }

  late final _$helpAndSupportAtom =
      Atom(name: '_RolesAndPermissionStore.helpAndSupport', context: context);

  @override
  bool get helpAndSupport {
    _$helpAndSupportAtom.reportRead();
    return super.helpAndSupport;
  }

  @override
  set helpAndSupport(bool value) {
    _$helpAndSupportAtom.reportWrite(value, super.helpAndSupport, () {
      super.helpAndSupport = value;
    });
  }

  late final _$termConditionAtom =
      Atom(name: '_RolesAndPermissionStore.termCondition', context: context);

  @override
  bool get termCondition {
    _$termConditionAtom.reportRead();
    return super.termCondition;
  }

  @override
  set termCondition(bool value) {
    _$termConditionAtom.reportWrite(value, super.termCondition, () {
      super.termCondition = value;
    });
  }

  late final _$privacyPolicyAtom =
      Atom(name: '_RolesAndPermissionStore.privacyPolicy', context: context);

  @override
  bool get privacyPolicy {
    _$privacyPolicyAtom.reportRead();
    return super.privacyPolicy;
  }

  @override
  set privacyPolicy(bool value) {
    _$privacyPolicyAtom.reportWrite(value, super.privacyPolicy, () {
      super.privacyPolicy = value;
    });
  }

  late final _$providerDocumentAtom =
      Atom(name: '_RolesAndPermissionStore.providerDocument', context: context);

  @override
  bool get providerDocument {
    _$providerDocumentAtom.reportRead();
    return super.providerDocument;
  }

  @override
  set providerDocument(bool value) {
    _$providerDocumentAtom.reportWrite(value, super.providerDocument, () {
      super.providerDocument = value;
    });
  }

  late final _$providerDocumentListAtom = Atom(
      name: '_RolesAndPermissionStore.providerDocumentList', context: context);

  @override
  bool get providerDocumentList {
    _$providerDocumentListAtom.reportRead();
    return super.providerDocumentList;
  }

  @override
  set providerDocumentList(bool value) {
    _$providerDocumentListAtom.reportWrite(value, super.providerDocumentList,
        () {
      super.providerDocumentList = value;
    });
  }

  late final _$providerDocumentAddAtom = Atom(
      name: '_RolesAndPermissionStore.providerDocumentAdd', context: context);

  @override
  bool get providerDocumentAdd {
    _$providerDocumentAddAtom.reportRead();
    return super.providerDocumentAdd;
  }

  @override
  set providerDocumentAdd(bool value) {
    _$providerDocumentAddAtom.reportWrite(value, super.providerDocumentAdd, () {
      super.providerDocumentAdd = value;
    });
  }

  late final _$providerDocumentEditAtom = Atom(
      name: '_RolesAndPermissionStore.providerDocumentEdit', context: context);

  @override
  bool get providerDocumentEdit {
    _$providerDocumentEditAtom.reportRead();
    return super.providerDocumentEdit;
  }

  @override
  set providerDocumentEdit(bool value) {
    _$providerDocumentEditAtom.reportWrite(value, super.providerDocumentEdit,
        () {
      super.providerDocumentEdit = value;
    });
  }

  late final _$providerDocumentDeleteAtom = Atom(
      name: '_RolesAndPermissionStore.providerDocumentDelete',
      context: context);

  @override
  bool get providerDocumentDelete {
    _$providerDocumentDeleteAtom.reportRead();
    return super.providerDocumentDelete;
  }

  @override
  set providerDocumentDelete(bool value) {
    _$providerDocumentDeleteAtom
        .reportWrite(value, super.providerDocumentDelete, () {
      super.providerDocumentDelete = value;
    });
  }

  late final _$setRoleAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setRole', context: context);

  @override
  Future<void> setRole(bool val) {
    return _$setRoleAsyncAction.run(() => super.setRole(val));
  }

  late final _$setRoleAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setRoleAdd', context: context);

  @override
  Future<void> setRoleAdd(bool val) {
    return _$setRoleAddAsyncAction.run(() => super.setRoleAdd(val));
  }

  late final _$setRoleListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setRoleList', context: context);

  @override
  Future<void> setRoleList(bool val) {
    return _$setRoleListAsyncAction.run(() => super.setRoleList(val));
  }

  late final _$setPermissionAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPermission', context: context);

  @override
  Future<void> setPermission(bool val) {
    return _$setPermissionAsyncAction.run(() => super.setPermission(val));
  }

  late final _$setPermissionAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setPermissionAdd',
      context: context);

  @override
  Future<void> setPermissionAdd(bool val) {
    return _$setPermissionAddAsyncAction.run(() => super.setPermissionAdd(val));
  }

  late final _$setPermissionListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setPermissionList',
      context: context);

  @override
  Future<void> setPermissionList(bool val) {
    return _$setPermissionListAsyncAction
        .run(() => super.setPermissionList(val));
  }

  late final _$setCategoryAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCategory', context: context);

  @override
  Future<void> setCategory(bool val) {
    return _$setCategoryAsyncAction.run(() => super.setCategory(val));
  }

  late final _$setCategoryAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCategoryAdd', context: context);

  @override
  Future<void> setCategoryAdd(bool val) {
    return _$setCategoryAddAsyncAction.run(() => super.setCategoryAdd(val));
  }

  late final _$setCategoryListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCategoryList', context: context);

  @override
  Future<void> setCategoryList(bool val) {
    return _$setCategoryListAsyncAction.run(() => super.setCategoryList(val));
  }

  late final _$setCategoryEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCategoryEdit', context: context);

  @override
  Future<void> setCategoryEdit(bool val) {
    return _$setCategoryEditAsyncAction.run(() => super.setCategoryEdit(val));
  }

  late final _$setCategoryDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setCategoryDelete',
      context: context);

  @override
  Future<void> setCategoryDelete(bool val) {
    return _$setCategoryDeleteAsyncAction
        .run(() => super.setCategoryDelete(val));
  }

  late final _$setServiceAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setService', context: context);

  @override
  Future<void> setService(bool val) {
    return _$setServiceAsyncAction.run(() => super.setService(val));
  }

  late final _$setServiceAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setServiceAdd', context: context);

  @override
  Future<void> setServiceAdd(bool val) {
    return _$setServiceAddAsyncAction.run(() => super.setServiceAdd(val));
  }

  late final _$setServiceListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setServiceList', context: context);

  @override
  Future<void> setServiceList(bool val) {
    return _$setServiceListAsyncAction.run(() => super.setServiceList(val));
  }

  late final _$setServiceEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setServiceEdit', context: context);

  @override
  Future<void> setServiceEdit(bool val) {
    return _$setServiceEditAsyncAction.run(() => super.setServiceEdit(val));
  }

  late final _$setServiceDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceDelete',
      context: context);

  @override
  Future<void> setServiceDelete(bool val) {
    return _$setServiceDeleteAsyncAction.run(() => super.setServiceDelete(val));
  }

  late final _$setProviderAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setProvider', context: context);

  @override
  Future<void> setProvider(bool val) {
    return _$setProviderAsyncAction.run(() => super.setProvider(val));
  }

  late final _$setProviderAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setProviderAdd', context: context);

  @override
  Future<void> setProviderAdd(bool val) {
    return _$setProviderAddAsyncAction.run(() => super.setProviderAdd(val));
  }

  late final _$setProviderListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setProviderList', context: context);

  @override
  Future<void> setProviderList(bool val) {
    return _$setProviderListAsyncAction.run(() => super.setProviderList(val));
  }

  late final _$setProviderEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setProviderEdit', context: context);

  @override
  Future<void> setProviderEdit(bool val) {
    return _$setProviderEditAsyncAction.run(() => super.setProviderEdit(val));
  }

  late final _$setProviderDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderDelete',
      context: context);

  @override
  Future<void> setProviderDelete(bool val) {
    return _$setProviderDeleteAsyncAction
        .run(() => super.setProviderDelete(val));
  }

  late final _$setHandymanAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHandyman', context: context);

  @override
  Future<void> setHandyman(bool val) {
    return _$setHandymanAsyncAction.run(() => super.setHandyman(val));
  }

  late final _$setHandymanListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHandymanList', context: context);

  @override
  Future<void> setHandymanList(bool val) {
    return _$setHandymanListAsyncAction.run(() => super.setHandymanList(val));
  }

  late final _$setHandymanAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHandymanAdd', context: context);

  @override
  Future<void> setHandymanAdd(bool val) {
    return _$setHandymanAddAsyncAction.run(() => super.setHandymanAdd(val));
  }

  late final _$setHandymanEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHandymanEdit', context: context);

  @override
  Future<void> setHandymanEdit(bool val) {
    return _$setHandymanEditAsyncAction.run(() => super.setHandymanEdit(val));
  }

  late final _$setHandymanDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanDelete',
      context: context);

  @override
  Future<void> setHandymanDelete(bool val) {
    return _$setHandymanDeleteAsyncAction
        .run(() => super.setHandymanDelete(val));
  }

  late final _$setBookingAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBooking', context: context);

  @override
  Future<void> setBooking(bool val) {
    return _$setBookingAsyncAction.run(() => super.setBooking(val));
  }

  late final _$setBookingListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBookingList', context: context);

  @override
  Future<void> setBookingList(bool val) {
    return _$setBookingListAsyncAction.run(() => super.setBookingList(val));
  }

  late final _$setBookingEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBookingEdit', context: context);

  @override
  Future<void> setBookingEdit(bool val) {
    return _$setBookingEditAsyncAction.run(() => super.setBookingEdit(val));
  }

  late final _$setBookingDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setBookingDelete',
      context: context);

  @override
  Future<void> setBookingDelete(bool val) {
    return _$setBookingDeleteAsyncAction.run(() => super.setBookingDelete(val));
  }

  late final _$setBookingViewAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBookingView', context: context);

  @override
  Future<void> setBookingView(bool val) {
    return _$setBookingViewAsyncAction.run(() => super.setBookingView(val));
  }

  late final _$setPaymentAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPayment', context: context);

  @override
  Future<void> setPayment(bool val) {
    return _$setPaymentAsyncAction.run(() => super.setPayment(val));
  }

  late final _$setPaymentListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPaymentList', context: context);

  @override
  Future<void> setPaymentList(bool val) {
    return _$setPaymentListAsyncAction.run(() => super.setPaymentList(val));
  }

  late final _$setUserAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUser', context: context);

  @override
  Future<void> setUser(bool val) {
    return _$setUserAsyncAction.run(() => super.setUser(val));
  }

  late final _$setUserListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUserList', context: context);

  @override
  Future<void> setUserList(bool val) {
    return _$setUserListAsyncAction.run(() => super.setUserList(val));
  }

  late final _$setUserViewAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUserView', context: context);

  @override
  Future<void> setUserView(bool val) {
    return _$setUserViewAsyncAction.run(() => super.setUserView(val));
  }

  late final _$setUserDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUserDelete', context: context);

  @override
  Future<void> setUserDelete(bool val) {
    return _$setUserDeleteAsyncAction.run(() => super.setUserDelete(val));
  }

  late final _$setProviderTypeAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setProviderType', context: context);

  @override
  Future<void> setProviderType(bool val) {
    return _$setProviderTypeAsyncAction.run(() => super.setProviderType(val));
  }

  late final _$setProviderTypeListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderTypeList',
      context: context);

  @override
  Future<void> setProviderTypeList(bool val) {
    return _$setProviderTypeListAsyncAction
        .run(() => super.setProviderTypeList(val));
  }

  late final _$setProviderTypeAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderTypeAdd',
      context: context);

  @override
  Future<void> setProviderTypeAdd(bool val) {
    return _$setProviderTypeAddAsyncAction
        .run(() => super.setProviderTypeAdd(val));
  }

  late final _$setProviderTypeEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderTypeEdit',
      context: context);

  @override
  Future<void> setProviderTypeEdit(bool val) {
    return _$setProviderTypeEditAsyncAction
        .run(() => super.setProviderTypeEdit(val));
  }

  late final _$setProviderTypeDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderTypeDelete',
      context: context);

  @override
  Future<void> setProviderTypeDelete(bool val) {
    return _$setProviderTypeDeleteAsyncAction
        .run(() => super.setProviderTypeDelete(val));
  }

  late final _$setCouponAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCoupon', context: context);

  @override
  Future<void> setCoupon(bool val) {
    return _$setCouponAsyncAction.run(() => super.setCoupon(val));
  }

  late final _$setCouponListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCouponList', context: context);

  @override
  Future<void> setCouponList(bool val) {
    return _$setCouponListAsyncAction.run(() => super.setCouponList(val));
  }

  late final _$setCouponAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCouponAdd', context: context);

  @override
  Future<void> setCouponAdd(bool val) {
    return _$setCouponAddAsyncAction.run(() => super.setCouponAdd(val));
  }

  late final _$setCouponEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCouponEdit', context: context);

  @override
  Future<void> setCouponEdit(bool val) {
    return _$setCouponEditAsyncAction.run(() => super.setCouponEdit(val));
  }

  late final _$setCouponDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setCouponDelete', context: context);

  @override
  Future<void> setCouponDelete(bool val) {
    return _$setCouponDeleteAsyncAction.run(() => super.setCouponDelete(val));
  }

  late final _$setSliderAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setSlider', context: context);

  @override
  Future<void> setSlider(bool val) {
    return _$setSliderAsyncAction.run(() => super.setSlider(val));
  }

  late final _$setSliderListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setSliderList', context: context);

  @override
  Future<void> setSliderList(bool val) {
    return _$setSliderListAsyncAction.run(() => super.setSliderList(val));
  }

  late final _$setSliderAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setSliderAdd', context: context);

  @override
  Future<void> setSliderAdd(bool val) {
    return _$setSliderAddAsyncAction.run(() => super.setSliderAdd(val));
  }

  late final _$setSliderEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setSliderEdit', context: context);

  @override
  Future<void> setSliderEdit(bool val) {
    return _$setSliderEditAsyncAction.run(() => super.setSliderEdit(val));
  }

  late final _$setSliderDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setSliderDelete', context: context);

  @override
  Future<void> setSliderDelete(bool val) {
    return _$setSliderDeleteAsyncAction.run(() => super.setSliderDelete(val));
  }

  late final _$setProviderAddressAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderAddress',
      context: context);

  @override
  Future<void> setProviderAddress(bool val) {
    return _$setProviderAddressAsyncAction
        .run(() => super.setProviderAddress(val));
  }

  late final _$setProviderAddressListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderAddressList',
      context: context);

  @override
  Future<void> setProviderAddressList(bool val) {
    return _$setProviderAddressListAsyncAction
        .run(() => super.setProviderAddressList(val));
  }

  late final _$setProviderAddressAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderAddressAdd',
      context: context);

  @override
  Future<void> setProviderAddressAdd(bool val) {
    return _$setProviderAddressAddAsyncAction
        .run(() => super.setProviderAddressAdd(val));
  }

  late final _$setProviderAddressEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderAddressEdit',
      context: context);

  @override
  Future<void> setProviderAddressEdit(bool val) {
    return _$setProviderAddressEditAsyncAction
        .run(() => super.setProviderAddressEdit(val));
  }

  late final _$setProviderAddressDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderAddressDelete',
      context: context);

  @override
  Future<void> setProviderAddressDelete(bool val) {
    return _$setProviderAddressDeleteAsyncAction
        .run(() => super.setProviderAddressDelete(val));
  }

  late final _$setDocumentAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setDocument', context: context);

  @override
  Future<void> setDocument(bool val) {
    return _$setDocumentAsyncAction.run(() => super.setDocument(val));
  }

  late final _$setDocumentListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setDocumentList', context: context);

  @override
  Future<void> setDocumentList(bool val) {
    return _$setDocumentListAsyncAction.run(() => super.setDocumentList(val));
  }

  late final _$setDocumentAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setDocumentAdd', context: context);

  @override
  Future<void> setDocumentAdd(bool val) {
    return _$setDocumentAddAsyncAction.run(() => super.setDocumentAdd(val));
  }

  late final _$setDocumentEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setDocumentEdit', context: context);

  @override
  Future<void> setDocumentEdit(bool val) {
    return _$setDocumentEditAsyncAction.run(() => super.setDocumentEdit(val));
  }

  late final _$setDocumentDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setDocumentDelete',
      context: context);

  @override
  Future<void> setDocumentDelete(bool val) {
    return _$setDocumentDeleteAsyncAction
        .run(() => super.setDocumentDelete(val));
  }

  late final _$setHandymanPayoutAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanPayout',
      context: context);

  @override
  Future<void> setHandymanPayout(bool val) {
    return _$setHandymanPayoutAsyncAction
        .run(() => super.setHandymanPayout(val));
  }

  late final _$setServiceFAQAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setServiceFAQ', context: context);

  @override
  Future<void> setServiceFAQ(bool val) {
    return _$setServiceFAQAsyncAction.run(() => super.setServiceFAQ(val));
  }

  late final _$setServiceFAQAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceFAQAdd',
      context: context);

  @override
  Future<void> setServiceFAQAdd(bool val) {
    return _$setServiceFAQAddAsyncAction.run(() => super.setServiceFAQAdd(val));
  }

  late final _$setServiceFAQEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceFAQEdit',
      context: context);

  @override
  Future<void> setServiceFAQEdit(bool val) {
    return _$setServiceFAQEditAsyncAction
        .run(() => super.setServiceFAQEdit(val));
  }

  late final _$setServiceFAQDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceFAQDelete',
      context: context);

  @override
  Future<void> setServiceFAQDelete(bool val) {
    return _$setServiceFAQDeleteAsyncAction
        .run(() => super.setServiceFAQDelete(val));
  }

  late final _$setServiceFAQListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceFAQList',
      context: context);

  @override
  Future<void> setServiceFAQList(bool val) {
    return _$setServiceFAQListAsyncAction
        .run(() => super.setServiceFAQList(val));
  }

  late final _$setUserAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUserAdd', context: context);

  @override
  Future<void> setUserAdd(bool val) {
    return _$setUserAddAsyncAction.run(() => super.setUserAdd(val));
  }

  late final _$setUserEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUserEdit', context: context);

  @override
  Future<void> setUserEdit(bool val) {
    return _$setUserEditAsyncAction.run(() => super.setUserEdit(val));
  }

  late final _$setSubcategoryAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setSubcategory', context: context);

  @override
  Future<void> setSubcategory(bool val) {
    return _$setSubcategoryAsyncAction.run(() => super.setSubcategory(val));
  }

  late final _$setSubcategoryAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setSubcategoryAdd',
      context: context);

  @override
  Future<void> setSubcategoryAdd(bool val) {
    return _$setSubcategoryAddAsyncAction
        .run(() => super.setSubcategoryAdd(val));
  }

  late final _$setSubcategoryEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setSubcategoryEdit',
      context: context);

  @override
  Future<void> setSubcategoryEdit(bool val) {
    return _$setSubcategoryEditAsyncAction
        .run(() => super.setSubcategoryEdit(val));
  }

  late final _$setSubcategoryDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setSubcategoryDelete',
      context: context);

  @override
  Future<void> setSubcategoryDelete(bool val) {
    return _$setSubcategoryDeleteAsyncAction
        .run(() => super.setSubcategoryDelete(val));
  }

  late final _$setSubcategoryListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setSubcategoryList',
      context: context);

  @override
  Future<void> setSubcategoryList(bool val) {
    return _$setSubcategoryListAsyncAction
        .run(() => super.setSubcategoryList(val));
  }

  late final _$setHandymanTypeAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHandymanType', context: context);

  @override
  Future<void> setHandymanType(bool val) {
    return _$setHandymanTypeAsyncAction.run(() => super.setHandymanType(val));
  }

  late final _$setHandymanTypeListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanTypeList',
      context: context);

  @override
  Future<void> setHandymanTypeList(bool val) {
    return _$setHandymanTypeListAsyncAction
        .run(() => super.setHandymanTypeList(val));
  }

  late final _$setHandymanTypeAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanTypeAdd',
      context: context);

  @override
  Future<void> setHandymanTypeAdd(bool val) {
    return _$setHandymanTypeAddAsyncAction
        .run(() => super.setHandymanTypeAdd(val));
  }

  late final _$setHandymanTypeEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanTypeEdit',
      context: context);

  @override
  Future<void> setHandymanTypeEdit(bool val) {
    return _$setHandymanTypeEditAsyncAction
        .run(() => super.setHandymanTypeEdit(val));
  }

  late final _$setHandymanTypeDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanTypeDelete',
      context: context);

  @override
  Future<void> setHandymanTypeDelete(bool val) {
    return _$setHandymanTypeDeleteAsyncAction
        .run(() => super.setHandymanTypeDelete(val));
  }

  late final _$setPostJobAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPostJob', context: context);

  @override
  Future<void> setPostJob(bool val) {
    return _$setPostJobAsyncAction.run(() => super.setPostJob(val));
  }

  late final _$setPostJobListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPostJobList', context: context);

  @override
  Future<void> setPostJobList(bool val) {
    return _$setPostJobListAsyncAction.run(() => super.setPostJobList(val));
  }

  late final _$setServicePackageAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServicePackage',
      context: context);

  @override
  Future<void> setServicePackage(bool val) {
    return _$setServicePackageAsyncAction
        .run(() => super.setServicePackage(val));
  }

  late final _$setServicePackageAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServicePackageAdd',
      context: context);

  @override
  Future<void> setServicePackageAdd(bool val) {
    return _$setServicePackageAddAsyncAction
        .run(() => super.setServicePackageAdd(val));
  }

  late final _$setServicePackageEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServicePackageEdit',
      context: context);

  @override
  Future<void> setServicePackageEdit(bool val) {
    return _$setServicePackageEditAsyncAction
        .run(() => super.setServicePackageEdit(val));
  }

  late final _$setServicePackageDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServicePackageDelete',
      context: context);

  @override
  Future<void> setServicePackageDelete(bool val) {
    return _$setServicePackageDeleteAsyncAction
        .run(() => super.setServicePackageDelete(val));
  }

  late final _$setServicePackageListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServicePackageList',
      context: context);

  @override
  Future<void> setServicePackageList(bool val) {
    return _$setServicePackageListAsyncAction
        .run(() => super.setServicePackageList(val));
  }

  late final _$setRefundAndCancellationPolicyAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setRefundAndCancellationPolicy',
      context: context);

  @override
  Future<void> setRefundAndCancellationPolicy(bool val) {
    return _$setRefundAndCancellationPolicyAsyncAction
        .run(() => super.setRefundAndCancellationPolicy(val));
  }

  late final _$setBlogAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBlog', context: context);

  @override
  Future<void> setBlog(bool val) {
    return _$setBlogAsyncAction.run(() => super.setBlog(val));
  }

  late final _$setBlogAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBlogAdd', context: context);

  @override
  Future<void> setBlogAdd(bool val) {
    return _$setBlogAddAsyncAction.run(() => super.setBlogAdd(val));
  }

  late final _$setBlogEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBlogEdit', context: context);

  @override
  Future<void> setBlogEdit(bool val) {
    return _$setBlogEditAsyncAction.run(() => super.setBlogEdit(val));
  }

  late final _$setBlogDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBlogDelete', context: context);

  @override
  Future<void> setBlogDelete(bool val) {
    return _$setBlogDeleteAsyncAction.run(() => super.setBlogDelete(val));
  }

  late final _$setBlogListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBlogList', context: context);

  @override
  Future<void> setBlogList(bool val) {
    return _$setBlogListAsyncAction.run(() => super.setBlogList(val));
  }

  late final _$setServiceAddOnAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setServiceAddOn', context: context);

  @override
  Future<void> setServiceAddOn(bool val) {
    return _$setServiceAddOnAsyncAction.run(() => super.setServiceAddOn(val));
  }

  late final _$setServiceAddOnAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceAddOnAdd',
      context: context);

  @override
  Future<void> setServiceAddOnAdd(bool val) {
    return _$setServiceAddOnAddAsyncAction
        .run(() => super.setServiceAddOnAdd(val));
  }

  late final _$setServiceAddOnEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceAddOnEdit',
      context: context);

  @override
  Future<void> setServiceAddOnEdit(bool val) {
    return _$setServiceAddOnEditAsyncAction
        .run(() => super.setServiceAddOnEdit(val));
  }

  late final _$setServiceAddOnListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setServiceAddOnList',
      context: context);

  @override
  Future<void> setServiceAddOnList(bool val) {
    return _$setServiceAddOnListAsyncAction
        .run(() => super.setServiceAddOnList(val));
  }

  late final _$setFrontendSettingAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setFrontendSetting',
      context: context);

  @override
  Future<void> setFrontendSetting(bool val) {
    return _$setFrontendSettingAsyncAction
        .run(() => super.setFrontendSetting(val));
  }

  late final _$setFrontendSettingListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setFrontendSettingList',
      context: context);

  @override
  Future<void> setFrontendSettingList(bool val) {
    return _$setFrontendSettingListAsyncAction
        .run(() => super.setFrontendSettingList(val));
  }

  late final _$setBankAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBank', context: context);

  @override
  Future<void> setBank(bool val) {
    return _$setBankAsyncAction.run(() => super.setBank(val));
  }

  late final _$setBankAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBankAdd', context: context);

  @override
  Future<void> setBankAdd(bool val) {
    return _$setBankAddAsyncAction.run(() => super.setBankAdd(val));
  }

  late final _$setBankEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBankEdit', context: context);

  @override
  Future<void> setBankEdit(bool val) {
    return _$setBankEditAsyncAction.run(() => super.setBankEdit(val));
  }

  late final _$setBankDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBankDelete', context: context);

  @override
  Future<void> setBankDelete(bool val) {
    return _$setBankDeleteAsyncAction.run(() => super.setBankDelete(val));
  }

  late final _$setBankListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setBankList', context: context);

  @override
  Future<void> setBankList(bool val) {
    return _$setBankListAsyncAction.run(() => super.setBankList(val));
  }

  late final _$setTaxAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setTax', context: context);

  @override
  Future<void> setTax(bool val) {
    return _$setTaxAsyncAction.run(() => super.setTax(val));
  }

  late final _$setTaxAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setTaxAdd', context: context);

  @override
  Future<void> setTaxAdd(bool val) {
    return _$setTaxAddAsyncAction.run(() => super.setTaxAdd(val));
  }

  late final _$setTaxEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setTaxEdit', context: context);

  @override
  Future<void> setTaxEdit(bool val) {
    return _$setTaxEditAsyncAction.run(() => super.setTaxEdit(val));
  }

  late final _$setTaxDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setTaxDelete', context: context);

  @override
  Future<void> setTaxDelete(bool val) {
    return _$setTaxDeleteAsyncAction.run(() => super.setTaxDelete(val));
  }

  late final _$setTaxListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setTaxList', context: context);

  @override
  Future<void> setTaxList(bool val) {
    return _$setTaxListAsyncAction.run(() => super.setTaxList(val));
  }

  late final _$setEarningAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setEarning', context: context);

  @override
  Future<void> setEarning(bool val) {
    return _$setEarningAsyncAction.run(() => super.setEarning(val));
  }

  late final _$setEarningListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setEarningList', context: context);

  @override
  Future<void> setEarningList(bool val) {
    return _$setEarningListAsyncAction.run(() => super.setEarningList(val));
  }

  late final _$setWalletAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setWallet', context: context);

  @override
  Future<void> setWallet(bool val) {
    return _$setWalletAsyncAction.run(() => super.setWallet(val));
  }

  late final _$setWalletAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setWalletAdd', context: context);

  @override
  Future<void> setWalletAdd(bool val) {
    return _$setWalletAddAsyncAction.run(() => super.setWalletAdd(val));
  }

  late final _$setWalletEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setWalletEdit', context: context);

  @override
  Future<void> setWalletEdit(bool val) {
    return _$setWalletEditAsyncAction.run(() => super.setWalletEdit(val));
  }

  late final _$setWalletDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setWalletDelete', context: context);

  @override
  Future<void> setWalletDelete(bool val) {
    return _$setWalletDeleteAsyncAction.run(() => super.setWalletDelete(val));
  }

  late final _$setWalletListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setWalletList', context: context);

  @override
  Future<void> setWalletList(bool val) {
    return _$setWalletListAsyncAction.run(() => super.setWalletList(val));
  }

  late final _$setUserRatingAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setUserRating', context: context);

  @override
  Future<void> setUserRating(bool val) {
    return _$setUserRatingAsyncAction.run(() => super.setUserRating(val));
  }

  late final _$setUserRatingListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setUserRatingList',
      context: context);

  @override
  Future<void> setUserRatingList(bool val) {
    return _$setUserRatingListAsyncAction
        .run(() => super.setUserRatingList(val));
  }

  late final _$setHandymanRatingAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanRating',
      context: context);

  @override
  Future<void> setHandymanRating(bool val) {
    return _$setHandymanRatingAsyncAction
        .run(() => super.setHandymanRating(val));
  }

  late final _$setHandymanRatingListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHandymanRatingList',
      context: context);

  @override
  Future<void> setHandymanRatingList(bool val) {
    return _$setHandymanRatingListAsyncAction
        .run(() => super.setHandymanRatingList(val));
  }

  late final _$setProviderPayoutAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderPayout',
      context: context);

  @override
  Future<void> setProviderPayout(bool val) {
    return _$setProviderPayoutAsyncAction
        .run(() => super.setProviderPayout(val));
  }

  late final _$setPlanAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPlan', context: context);

  @override
  Future<void> setPlan(bool val) {
    return _$setPlanAsyncAction.run(() => super.setPlan(val));
  }

  late final _$setPlanAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPlanAdd', context: context);

  @override
  Future<void> setPlanAdd(bool val) {
    return _$setPlanAddAsyncAction.run(() => super.setPlanAdd(val));
  }

  late final _$setPlanEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPlanEdit', context: context);

  @override
  Future<void> setPlanEdit(bool val) {
    return _$setPlanEditAsyncAction.run(() => super.setPlanEdit(val));
  }

  late final _$setPlanDeleteAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPlanDelete', context: context);

  @override
  Future<void> setPlanDelete(bool val) {
    return _$setPlanDeleteAsyncAction.run(() => super.setPlanDelete(val));
  }

  late final _$setPlanListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPlanList', context: context);

  @override
  Future<void> setPlanList(bool val) {
    return _$setPlanListAsyncAction.run(() => super.setPlanList(val));
  }

  late final _$setUserServiceListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setUserServiceList',
      context: context);

  @override
  Future<void> setUserServiceList(bool val) {
    return _$setUserServiceListAsyncAction
        .run(() => super.setUserServiceList(val));
  }

  late final _$setSystemSettingAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setSystemSetting',
      context: context);

  @override
  Future<void> setSystemSetting(bool val) {
    return _$setSystemSettingAsyncAction.run(() => super.setSystemSetting(val));
  }

  late final _$setProviderChangePasswordAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderChangePassword',
      context: context);

  @override
  Future<void> setProviderChangePassword(bool val) {
    return _$setProviderChangePasswordAsyncAction
        .run(() => super.setProviderChangePassword(val));
  }

  late final _$setDataDeletionRequestAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setDataDeletionRequest',
      context: context);

  @override
  Future<void> setDataDeletionRequest(bool val) {
    return _$setDataDeletionRequestAsyncAction
        .run(() => super.setDataDeletionRequest(val));
  }

  late final _$setHelpDeskAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHelpDesk', context: context);

  @override
  Future<void> setHelpDesk(bool val) {
    return _$setHelpDeskAsyncAction.run(() => super.setHelpDesk(val));
  }

  late final _$setHelpDeskAddAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHelpDeskAdd', context: context);

  @override
  Future<void> setHelpDeskAdd(bool val) {
    return _$setHelpDeskAddAsyncAction.run(() => super.setHelpDeskAdd(val));
  }

  late final _$setHelpDeskEditAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHelpDeskEdit', context: context);

  @override
  Future<void> setHelpDeskEdit(bool val) {
    return _$setHelpDeskEditAsyncAction.run(() => super.setHelpDeskEdit(val));
  }

  late final _$setHelpDeskListAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setHelpDeskList', context: context);

  @override
  Future<void> setHelpDeskList(bool val) {
    return _$setHelpDeskListAsyncAction.run(() => super.setHelpDeskList(val));
  }

  late final _$setPendingProviderAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setPendingProvider',
      context: context);

  @override
  Future<void> setPendingProvider(bool val) {
    return _$setPendingProviderAsyncAction
        .run(() => super.setPendingProvider(val));
  }

  late final _$setPendingHandymanAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setPendingHandyman',
      context: context);

  @override
  Future<void> setPendingHandyman(bool val) {
    return _$setPendingHandymanAsyncAction
        .run(() => super.setPendingHandyman(val));
  }

  late final _$setPagesAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setPages', context: context);

  @override
  Future<void> setPages(bool val) {
    return _$setPagesAsyncAction.run(() => super.setPages(val));
  }

  late final _$setAboutUsAsyncAction =
      AsyncAction('_RolesAndPermissionStore.setAboutUs', context: context);

  @override
  Future<void> setAboutUs(bool val) {
    return _$setAboutUsAsyncAction.run(() => super.setAboutUs(val));
  }

  late final _$setHelpAndSupportAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setHelpAndSupport',
      context: context);

  @override
  Future<void> setHelpAndSupport(bool val) {
    return _$setHelpAndSupportAsyncAction
        .run(() => super.setHelpAndSupport(val));
  }

  late final _$setPrivacyPolicyAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setPrivacyPolicy',
      context: context);

  @override
  Future<void> setPrivacyPolicy(bool val) {
    return _$setPrivacyPolicyAsyncAction.run(() => super.setPrivacyPolicy(val));
  }

  late final _$setTermConditionAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setTermCondition',
      context: context);

  @override
  Future<void> setTermCondition(bool val) {
    return _$setTermConditionAsyncAction.run(() => super.setTermCondition(val));
  }

  late final _$setProviderDocumentAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderDocument',
      context: context);

  @override
  Future<void> setProviderDocument(bool val) {
    return _$setProviderDocumentAsyncAction
        .run(() => super.setProviderDocument(val));
  }

  late final _$setProviderDocumentListAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderDocumentList',
      context: context);

  @override
  Future<void> setProviderDocumentList(bool val) {
    return _$setProviderDocumentListAsyncAction
        .run(() => super.setProviderDocumentList(val));
  }

  late final _$setProviderDocumentAddAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderDocumentAdd',
      context: context);

  @override
  Future<void> setProviderDocumentAdd(bool val) {
    return _$setProviderDocumentAddAsyncAction
        .run(() => super.setProviderDocumentAdd(val));
  }

  late final _$setProviderDocumentEditAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderDocumentEdit',
      context: context);

  @override
  Future<void> setProviderDocumentEdit(bool val) {
    return _$setProviderDocumentEditAsyncAction
        .run(() => super.setProviderDocumentEdit(val));
  }

  late final _$setProviderDocumentDeleteAsyncAction = AsyncAction(
      '_RolesAndPermissionStore.setProviderDocumentDelete',
      context: context);

  @override
  Future<void> setProviderDocumentDelete(bool val) {
    return _$setProviderDocumentDeleteAsyncAction
        .run(() => super.setProviderDocumentDelete(val));
  }

  @override
  String toString() {
    return '''
role: ${role},
roleAdd: ${roleAdd},
roleList: ${roleList},
permission: ${permission},
permissionAdd: ${permissionAdd},
permissionList: ${permissionList},
category: ${category},
categoryAdd: ${categoryAdd},
categoryList: ${categoryList},
categoryEdit: ${categoryEdit},
categoryDelete: ${categoryDelete},
service: ${service},
serviceAdd: ${serviceAdd},
serviceList: ${serviceList},
serviceEdit: ${serviceEdit},
serviceDelete: ${serviceDelete},
provider: ${provider},
providerAdd: ${providerAdd},
providerList: ${providerList},
providerEdit: ${providerEdit},
providerDelete: ${providerDelete},
handyman: ${handyman},
handymanList: ${handymanList},
handymanAdd: ${handymanAdd},
handymanEdit: ${handymanEdit},
handymanDelete: ${handymanDelete},
booking: ${booking},
bookingList: ${bookingList},
bookingEdit: ${bookingEdit},
bookingDelete: ${bookingDelete},
bookingView: ${bookingView},
payment: ${payment},
paymentList: ${paymentList},
user: ${user},
userList: ${userList},
userView: ${userView},
userDelete: ${userDelete},
providerType: ${providerType},
providerTypeList: ${providerTypeList},
providerTypeAdd: ${providerTypeAdd},
providerTypeEdit: ${providerTypeEdit},
providerTypeDelete: ${providerTypeDelete},
coupon: ${coupon},
couponList: ${couponList},
couponAdd: ${couponAdd},
couponEdit: ${couponEdit},
couponDelete: ${couponDelete},
slider: ${slider},
sliderList: ${sliderList},
sliderAdd: ${sliderAdd},
sliderEdit: ${sliderEdit},
sliderDelete: ${sliderDelete},
providerAddress: ${providerAddress},
providerAddressList: ${providerAddressList},
providerAddressAdd: ${providerAddressAdd},
providerAddressEdit: ${providerAddressEdit},
providerAddressDelete: ${providerAddressDelete},
document: ${document},
documentList: ${documentList},
documentAdd: ${documentAdd},
documentEdit: ${documentEdit},
documentDelete: ${documentDelete},
handymanPayout: ${handymanPayout},
serviceFAQ: ${serviceFAQ},
serviceFAQAdd: ${serviceFAQAdd},
serviceFAQEdit: ${serviceFAQEdit},
serviceFAQDelete: ${serviceFAQDelete},
serviceFAQList: ${serviceFAQList},
userAdd: ${userAdd},
userEdit: ${userEdit},
subCategory: ${subCategory},
subCategoryAdd: ${subCategoryAdd},
subCategoryEdit: ${subCategoryEdit},
subCategoryDelete: ${subCategoryDelete},
subCategoryList: ${subCategoryList},
handymanType: ${handymanType},
handymanTypeList: ${handymanTypeList},
handymanTypeAdd: ${handymanTypeAdd},
handymanTypeEdit: ${handymanTypeEdit},
handymanTypeDelete: ${handymanTypeDelete},
postJob: ${postJob},
postJobList: ${postJobList},
servicePackage: ${servicePackage},
servicePackageAdd: ${servicePackageAdd},
servicePackageEdit: ${servicePackageEdit},
servicePackageDelete: ${servicePackageDelete},
servicePackageList: ${servicePackageList},
refundAndCancellationPolicy: ${refundAndCancellationPolicy},
blog: ${blog},
blogAdd: ${blogAdd},
blogEdit: ${blogEdit},
blogDelete: ${blogDelete},
blogList: ${blogList},
serviceAddOn: ${serviceAddOn},
serviceAddOnAdd: ${serviceAddOnAdd},
serviceAddOnEdit: ${serviceAddOnEdit},
serviceAddOnList: ${serviceAddOnList},
frontendSetting: ${frontendSetting},
frontendSettingList: ${frontendSettingList},
bank: ${bank},
bankAdd: ${bankAdd},
bankEdit: ${bankEdit},
bankDelete: ${bankDelete},
bankList: ${bankList},
tax: ${tax},
taxAdd: ${taxAdd},
taxEdit: ${taxEdit},
taxDelete: ${taxDelete},
taxList: ${taxList},
earning: ${earning},
earningList: ${earningList},
wallet: ${wallet},
walletAdd: ${walletAdd},
walletEdit: ${walletEdit},
walletDelete: ${walletDelete},
walletList: ${walletList},
userRating: ${userRating},
userRatingList: ${userRatingList},
handymanRating: ${handymanRating},
handymanRatingList: ${handymanRatingList},
providerPayout: ${providerPayout},
plan: ${plan},
planAdd: ${planAdd},
planEdit: ${planEdit},
planDelete: ${planDelete},
planList: ${planList},
userServiceList: ${userServiceList},
systemSetting: ${systemSetting},
providerChangePassword: ${providerChangePassword},
dataDeletionRequest: ${dataDeletionRequest},
helpDesk: ${helpDesk},
helpDeskAdd: ${helpDeskAdd},
helpDeskEdit: ${helpDeskEdit},
helpDeskList: ${helpDeskList},
pendingProvider: ${pendingProvider},
pendingHandyman: ${pendingHandyman},
pages: ${pages},
aboutUs: ${aboutUs},
helpAndSupport: ${helpAndSupport},
termCondition: ${termCondition},
privacyPolicy: ${privacyPolicy},
providerDocument: ${providerDocument},
providerDocumentList: ${providerDocumentList},
providerDocumentAdd: ${providerDocumentAdd},
providerDocumentEdit: ${providerDocumentEdit},
providerDocumentDelete: ${providerDocumentDelete}
    ''';
  }
}
