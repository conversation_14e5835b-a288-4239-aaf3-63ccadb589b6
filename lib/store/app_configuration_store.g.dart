// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_configuration_store.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AppConfigurationStore on _AppConfigurationStore, Store {
  late final _$priceDecimalPointAtom =
      Atom(name: '_AppConfigurationStore.priceDecimalPoint', context: context);

  @override
  int get priceDecimalPoint {
    _$priceDecimalPointAtom.reportRead();
    return super.priceDecimalPoint;
  }

  @override
  set priceDecimalPoint(int value) {
    _$priceDecimalPointAtom.reportWrite(value, super.priceDecimalPoint, () {
      super.priceDecimalPoint = value;
    });
  }

  late final _$jobRequestStatusAtom =
      Atom(name: '_AppConfigurationStore.jobRequestStatus', context: context);

  @override
  bool get jobRequestStatus {
    _$jobRequestStatusAtom.reportRead();
    return super.jobRequestStatus;
  }

  @override
  set jobRequestStatus(bool value) {
    _$jobRequestStatusAtom.reportWrite(value, super.jobRequestStatus, () {
      super.jobRequestStatus = value;
    });
  }

  late final _$blogStatusAtom =
      Atom(name: '_AppConfigurationStore.blogStatus', context: context);

  @override
  bool get blogStatus {
    _$blogStatusAtom.reportRead();
    return super.blogStatus;
  }

  @override
  set blogStatus(bool value) {
    _$blogStatusAtom.reportWrite(value, super.blogStatus, () {
      super.blogStatus = value;
    });
  }

  late final _$socialLoginStatusAtom =
      Atom(name: '_AppConfigurationStore.socialLoginStatus', context: context);

  @override
  bool get socialLoginStatus {
    _$socialLoginStatusAtom.reportRead();
    return super.socialLoginStatus;
  }

  @override
  set socialLoginStatus(bool value) {
    _$socialLoginStatusAtom.reportWrite(value, super.socialLoginStatus, () {
      super.socialLoginStatus = value;
    });
  }

  late final _$googleLoginStatusAtom =
      Atom(name: '_AppConfigurationStore.googleLoginStatus', context: context);

  @override
  bool get googleLoginStatus {
    _$googleLoginStatusAtom.reportRead();
    return super.googleLoginStatus;
  }

  @override
  set googleLoginStatus(bool value) {
    _$googleLoginStatusAtom.reportWrite(value, super.googleLoginStatus, () {
      super.googleLoginStatus = value;
    });
  }

  late final _$appleLoginStatusAtom =
      Atom(name: '_AppConfigurationStore.appleLoginStatus', context: context);

  @override
  bool get appleLoginStatus {
    _$appleLoginStatusAtom.reportRead();
    return super.appleLoginStatus;
  }

  @override
  set appleLoginStatus(bool value) {
    _$appleLoginStatusAtom.reportWrite(value, super.appleLoginStatus, () {
      super.appleLoginStatus = value;
    });
  }

  late final _$otpLoginStatusAtom =
      Atom(name: '_AppConfigurationStore.otpLoginStatus', context: context);

  @override
  bool get otpLoginStatus {
    _$otpLoginStatusAtom.reportRead();
    return super.otpLoginStatus;
  }

  @override
  set otpLoginStatus(bool value) {
    _$otpLoginStatusAtom.reportWrite(value, super.otpLoginStatus, () {
      super.otpLoginStatus = value;
    });
  }

  late final _$maintenanceModeStatusAtom = Atom(
      name: '_AppConfigurationStore.maintenanceModeStatus', context: context);

  @override
  bool get maintenanceModeStatus {
    _$maintenanceModeStatusAtom.reportRead();
    return super.maintenanceModeStatus;
  }

  @override
  set maintenanceModeStatus(bool value) {
    _$maintenanceModeStatusAtom.reportWrite(value, super.maintenanceModeStatus,
        () {
      super.maintenanceModeStatus = value;
    });
  }

  late final _$chatGPTStatusAtom =
      Atom(name: '_AppConfigurationStore.chatGPTStatus', context: context);

  @override
  bool get chatGPTStatus {
    _$chatGPTStatusAtom.reportRead();
    return super.chatGPTStatus;
  }

  @override
  set chatGPTStatus(bool value) {
    _$chatGPTStatusAtom.reportWrite(value, super.chatGPTStatus, () {
      super.chatGPTStatus = value;
    });
  }

  late final _$testWithoutKeyAtom =
      Atom(name: '_AppConfigurationStore.testWithoutKey', context: context);

  @override
  bool get testWithoutKey {
    _$testWithoutKeyAtom.reportRead();
    return super.testWithoutKey;
  }

  @override
  set testWithoutKey(bool value) {
    _$testWithoutKeyAtom.reportWrite(value, super.testWithoutKey, () {
      super.testWithoutKey = value;
    });
  }

  late final _$firebaseServerKeyAtom =
      Atom(name: '_AppConfigurationStore.firebaseServerKey', context: context);

  @override
  String get firebaseServerKey {
    _$firebaseServerKeyAtom.reportRead();
    return super.firebaseServerKey;
  }

  @override
  set firebaseServerKey(String value) {
    _$firebaseServerKeyAtom.reportWrite(value, super.firebaseServerKey, () {
      super.firebaseServerKey = value;
    });
  }

  late final _$googleMapKeyAtom =
      Atom(name: '_AppConfigurationStore.googleMapKey', context: context);

  @override
  String get googleMapKey {
    _$googleMapKeyAtom.reportRead();
    return super.googleMapKey;
  }

  @override
  set googleMapKey(String value) {
    _$googleMapKeyAtom.reportWrite(value, super.googleMapKey, () {
      super.googleMapKey = value;
    });
  }

  late final _$inquiryEmailAtom =
      Atom(name: '_AppConfigurationStore.inquiryEmail', context: context);

  @override
  String get inquiryEmail {
    _$inquiryEmailAtom.reportRead();
    return super.inquiryEmail;
  }

  @override
  set inquiryEmail(String value) {
    _$inquiryEmailAtom.reportWrite(value, super.inquiryEmail, () {
      super.inquiryEmail = value;
    });
  }

  late final _$helplineNumberAtom =
      Atom(name: '_AppConfigurationStore.helplineNumber', context: context);

  @override
  String get helplineNumber {
    _$helplineNumberAtom.reportRead();
    return super.helplineNumber;
  }

  @override
  set helplineNumber(String value) {
    _$helplineNumberAtom.reportWrite(value, super.helplineNumber, () {
      super.helplineNumber = value;
    });
  }

  late final _$currencyPositionAtom =
      Atom(name: '_AppConfigurationStore.currencyPosition', context: context);

  @override
  String get currencyPosition {
    _$currencyPositionAtom.reportRead();
    return super.currencyPosition;
  }

  @override
  set currencyPosition(String value) {
    _$currencyPositionAtom.reportWrite(value, super.currencyPosition, () {
      super.currencyPosition = value;
    });
  }

  late final _$currencySymbolAtom =
      Atom(name: '_AppConfigurationStore.currencySymbol', context: context);

  @override
  String get currencySymbol {
    _$currencySymbolAtom.reportRead();
    return super.currencySymbol;
  }

  @override
  set currencySymbol(String value) {
    _$currencySymbolAtom.reportWrite(value, super.currencySymbol, () {
      super.currencySymbol = value;
    });
  }

  late final _$currencyCodeAtom =
      Atom(name: '_AppConfigurationStore.currencyCode', context: context);

  @override
  String get currencyCode {
    _$currencyCodeAtom.reportRead();
    return super.currencyCode;
  }

  @override
  set currencyCode(String value) {
    _$currencyCodeAtom.reportWrite(value, super.currencyCode, () {
      super.currencyCode = value;
    });
  }

  late final _$isEnableUserWalletAtom =
      Atom(name: '_AppConfigurationStore.isEnableUserWallet', context: context);

  @override
  bool get isEnableUserWallet {
    _$isEnableUserWalletAtom.reportRead();
    return super.isEnableUserWallet;
  }

  @override
  set isEnableUserWallet(bool value) {
    _$isEnableUserWalletAtom.reportWrite(value, super.isEnableUserWallet, () {
      super.isEnableUserWallet = value;
    });
  }

  late final _$isAdvancePaymentAllowedAtom = Atom(
      name: '_AppConfigurationStore.isAdvancePaymentAllowed', context: context);

  @override
  bool get isAdvancePaymentAllowed {
    _$isAdvancePaymentAllowedAtom.reportRead();
    return super.isAdvancePaymentAllowed;
  }

  @override
  set isAdvancePaymentAllowed(bool value) {
    _$isAdvancePaymentAllowedAtom
        .reportWrite(value, super.isAdvancePaymentAllowed, () {
      super.isAdvancePaymentAllowed = value;
    });
  }

  late final _$slotServiceStatusAtom =
      Atom(name: '_AppConfigurationStore.slotServiceStatus', context: context);

  @override
  bool get slotServiceStatus {
    _$slotServiceStatusAtom.reportRead();
    return super.slotServiceStatus;
  }

  @override
  set slotServiceStatus(bool value) {
    _$slotServiceStatusAtom.reportWrite(value, super.slotServiceStatus, () {
      super.slotServiceStatus = value;
    });
  }

  late final _$digitalServiceStatusAtom = Atom(
      name: '_AppConfigurationStore.digitalServiceStatus', context: context);

  @override
  bool get digitalServiceStatus {
    _$digitalServiceStatusAtom.reportRead();
    return super.digitalServiceStatus;
  }

  @override
  set digitalServiceStatus(bool value) {
    _$digitalServiceStatusAtom.reportWrite(value, super.digitalServiceStatus,
        () {
      super.digitalServiceStatus = value;
    });
  }

  late final _$servicePackageStatusAtom = Atom(
      name: '_AppConfigurationStore.servicePackageStatus', context: context);

  @override
  bool get servicePackageStatus {
    _$servicePackageStatusAtom.reportRead();
    return super.servicePackageStatus;
  }

  @override
  set servicePackageStatus(bool value) {
    _$servicePackageStatusAtom.reportWrite(value, super.servicePackageStatus,
        () {
      super.servicePackageStatus = value;
    });
  }

  late final _$serviceAddonStatusAtom =
      Atom(name: '_AppConfigurationStore.serviceAddonStatus', context: context);

  @override
  bool get serviceAddonStatus {
    _$serviceAddonStatusAtom.reportRead();
    return super.serviceAddonStatus;
  }

  @override
  set serviceAddonStatus(bool value) {
    _$serviceAddonStatusAtom.reportWrite(value, super.serviceAddonStatus, () {
      super.serviceAddonStatus = value;
    });
  }

  late final _$onlinePaymentStatusAtom = Atom(
      name: '_AppConfigurationStore.onlinePaymentStatus', context: context);

  @override
  bool get onlinePaymentStatus {
    _$onlinePaymentStatusAtom.reportRead();
    return super.onlinePaymentStatus;
  }

  @override
  set onlinePaymentStatus(bool value) {
    _$onlinePaymentStatusAtom.reportWrite(value, super.onlinePaymentStatus, () {
      super.onlinePaymentStatus = value;
    });
  }

  late final _$privacyPolicyAtom =
      Atom(name: '_AppConfigurationStore.privacyPolicy', context: context);

  @override
  String get privacyPolicy {
    _$privacyPolicyAtom.reportRead();
    return super.privacyPolicy;
  }

  @override
  set privacyPolicy(String value) {
    _$privacyPolicyAtom.reportWrite(value, super.privacyPolicy, () {
      super.privacyPolicy = value;
    });
  }

  late final _$termConditionsAtom =
      Atom(name: '_AppConfigurationStore.termConditions', context: context);

  @override
  String get termConditions {
    _$termConditionsAtom.reportRead();
    return super.termConditions;
  }

  @override
  set termConditions(String value) {
    _$termConditionsAtom.reportWrite(value, super.termConditions, () {
      super.termConditions = value;
    });
  }

  late final _$helpAndSupportAtom =
      Atom(name: '_AppConfigurationStore.helpAndSupport', context: context);

  @override
  String get helpAndSupport {
    _$helpAndSupportAtom.reportRead();
    return super.helpAndSupport;
  }

  @override
  set helpAndSupport(String value) {
    _$helpAndSupportAtom.reportWrite(value, super.helpAndSupport, () {
      super.helpAndSupport = value;
    });
  }

  late final _$refundPolicyAtom =
      Atom(name: '_AppConfigurationStore.refundPolicy', context: context);

  @override
  String get refundPolicy {
    _$refundPolicyAtom.reportRead();
    return super.refundPolicy;
  }

  @override
  set refundPolicy(String value) {
    _$refundPolicyAtom.reportWrite(value, super.refundPolicy, () {
      super.refundPolicy = value;
    });
  }

  late final _$userDashboardTypeAtom =
      Atom(name: '_AppConfigurationStore.userDashboardType', context: context);

  @override
  String get userDashboardType {
    _$userDashboardTypeAtom.reportRead();
    return super.userDashboardType;
  }

  @override
  set userDashboardType(String value) {
    _$userDashboardTypeAtom.reportWrite(value, super.userDashboardType, () {
      super.userDashboardType = value;
    });
  }

  late final _$isUserAuthorizedAtom =
      Atom(name: '_AppConfigurationStore.isUserAuthorized', context: context);

  @override
  bool get isUserAuthorized {
    _$isUserAuthorizedAtom.reportRead();
    return super.isUserAuthorized;
  }

  @override
  set isUserAuthorized(bool value) {
    _$isUserAuthorizedAtom.reportWrite(value, super.isUserAuthorized, () {
      super.isUserAuthorized = value;
    });
  }

  late final _$cancellationChargeAtom =
      Atom(name: '_AppConfigurationStore.cancellationCharge', context: context);

  @override
  bool get cancellationCharge {
    _$cancellationChargeAtom.reportRead();
    return super.cancellationCharge;
  }

  @override
  set cancellationCharge(bool value) {
    _$cancellationChargeAtom.reportWrite(value, super.cancellationCharge, () {
      super.cancellationCharge = value;
    });
  }

  late final _$cancellationChargeAmountAtom = Atom(
      name: '_AppConfigurationStore.cancellationChargeAmount',
      context: context);

  @override
  num get cancellationChargeAmount {
    _$cancellationChargeAmountAtom.reportRead();
    return super.cancellationChargeAmount;
  }

  @override
  set cancellationChargeAmount(num value) {
    _$cancellationChargeAmountAtom
        .reportWrite(value, super.cancellationChargeAmount, () {
      super.cancellationChargeAmount = value;
    });
  }

  late final _$cancellationChargeHoursAtom = Atom(
      name: '_AppConfigurationStore.cancellationChargeHours', context: context);

  @override
  int get cancellationChargeHours {
    _$cancellationChargeHoursAtom.reportRead();
    return super.cancellationChargeHours;
  }

  @override
  set cancellationChargeHours(int value) {
    _$cancellationChargeHoursAtom
        .reportWrite(value, super.cancellationChargeHours, () {
      super.cancellationChargeHours = value;
    });
  }

  late final _$isPromotionalBannerAtom = Atom(
      name: '_AppConfigurationStore.isPromotionalBanner', context: context);

  @override
  bool get isPromotionalBanner {
    _$isPromotionalBannerAtom.reportRead();
    return super.isPromotionalBanner;
  }

  @override
  set isPromotionalBanner(bool value) {
    _$isPromotionalBannerAtom.reportWrite(value, super.isPromotionalBanner, () {
      super.isPromotionalBanner = value;
    });
  }

  late final _$setPromotionalBannerStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setPromotionalBannerStatus',
      context: context);

  @override
  Future<void> setPromotionalBannerStatus(bool val) {
    return _$setPromotionalBannerStatusAsyncAction
        .run(() => super.setPromotionalBannerStatus(val));
  }

  late final _$setRefundPolicyAsyncAction =
      AsyncAction('_AppConfigurationStore.setRefundPolicy', context: context);

  @override
  Future<void> setRefundPolicy(String val) {
    return _$setRefundPolicyAsyncAction.run(() => super.setRefundPolicy(val));
  }

  late final _$setHelpAndSupportAsyncAction =
      AsyncAction('_AppConfigurationStore.setHelpAndSupport', context: context);

  @override
  Future<void> setHelpAndSupport(String val) {
    return _$setHelpAndSupportAsyncAction
        .run(() => super.setHelpAndSupport(val));
  }

  late final _$setPrivacyPolicyAsyncAction =
      AsyncAction('_AppConfigurationStore.setPrivacyPolicy', context: context);

  @override
  Future<void> setPrivacyPolicy(String val) {
    return _$setPrivacyPolicyAsyncAction.run(() => super.setPrivacyPolicy(val));
  }

  late final _$setTermConditionsAsyncAction =
      AsyncAction('_AppConfigurationStore.setTermConditions', context: context);

  @override
  Future<void> setTermConditions(String val) {
    return _$setTermConditionsAsyncAction
        .run(() => super.setTermConditions(val));
  }

  late final _$setAdvancePaymentAllowedAsyncAction = AsyncAction(
      '_AppConfigurationStore.setAdvancePaymentAllowed',
      context: context);

  @override
  Future<void> setAdvancePaymentAllowed(bool val) {
    return _$setAdvancePaymentAllowedAsyncAction
        .run(() => super.setAdvancePaymentAllowed(val));
  }

  late final _$setSlotServiceStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setSlotServiceStatus',
      context: context);

  @override
  Future<void> setSlotServiceStatus(bool val) {
    return _$setSlotServiceStatusAsyncAction
        .run(() => super.setSlotServiceStatus(val));
  }

  late final _$setDigitalServiceStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setDigitalServiceStatus',
      context: context);

  @override
  Future<void> setDigitalServiceStatus(bool val) {
    return _$setDigitalServiceStatusAsyncAction
        .run(() => super.setDigitalServiceStatus(val));
  }

  late final _$setServicePackageStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setServicePackageStatus',
      context: context);

  @override
  Future<void> setServicePackageStatus(bool val) {
    return _$setServicePackageStatusAsyncAction
        .run(() => super.setServicePackageStatus(val));
  }

  late final _$setServiceAddonStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setServiceAddonStatus',
      context: context);

  @override
  Future<void> setServiceAddonStatus(bool val) {
    return _$setServiceAddonStatusAsyncAction
        .run(() => super.setServiceAddonStatus(val));
  }

  late final _$setOnlinePaymentStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setOnlinePaymentStatus',
      context: context);

  @override
  Future<void> setOnlinePaymentStatus(bool val) {
    return _$setOnlinePaymentStatusAsyncAction
        .run(() => super.setOnlinePaymentStatus(val));
  }

  late final _$setPriceDecimalPointAsyncAction = AsyncAction(
      '_AppConfigurationStore.setPriceDecimalPoint',
      context: context);

  @override
  Future<void> setPriceDecimalPoint(int val) {
    return _$setPriceDecimalPointAsyncAction
        .run(() => super.setPriceDecimalPoint(val));
  }

  late final _$setEnableUserWalletAsyncAction = AsyncAction(
      '_AppConfigurationStore.setEnableUserWallet',
      context: context);

  @override
  Future<void> setEnableUserWallet(bool val) {
    return _$setEnableUserWalletAsyncAction
        .run(() => super.setEnableUserWallet(val));
  }

  late final _$setInquiryEmailAsyncAction =
      AsyncAction('_AppConfigurationStore.setInquiryEmail', context: context);

  @override
  Future<void> setInquiryEmail(String val) {
    return _$setInquiryEmailAsyncAction.run(() => super.setInquiryEmail(val));
  }

  late final _$setHelplineNumberAsyncAction =
      AsyncAction('_AppConfigurationStore.setHelplineNumber', context: context);

  @override
  Future<void> setHelplineNumber(String val) {
    return _$setHelplineNumberAsyncAction
        .run(() => super.setHelplineNumber(val));
  }

  late final _$setCurrencyPositionAsyncAction = AsyncAction(
      '_AppConfigurationStore.setCurrencyPosition',
      context: context);

  @override
  Future<void> setCurrencyPosition(String val) {
    return _$setCurrencyPositionAsyncAction
        .run(() => super.setCurrencyPosition(val));
  }

  late final _$setCurrencySymbolAsyncAction =
      AsyncAction('_AppConfigurationStore.setCurrencySymbol', context: context);

  @override
  Future<void> setCurrencySymbol(String val) {
    return _$setCurrencySymbolAsyncAction
        .run(() => super.setCurrencySymbol(val));
  }

  late final _$setCurrencyCodeAsyncAction =
      AsyncAction('_AppConfigurationStore.setCurrencyCode', context: context);

  @override
  Future<void> setCurrencyCode(String val) {
    return _$setCurrencyCodeAsyncAction.run(() => super.setCurrencyCode(val));
  }

  late final _$setChatGptStatusAsyncAction =
      AsyncAction('_AppConfigurationStore.setChatGptStatus', context: context);

  @override
  Future<void> setChatGptStatus(bool val) {
    return _$setChatGptStatusAsyncAction.run(() => super.setChatGptStatus(val));
  }

  late final _$setTestWithoutKeyAsyncAction =
      AsyncAction('_AppConfigurationStore.setTestWithoutKey', context: context);

  @override
  Future<void> setTestWithoutKey(bool val) {
    return _$setTestWithoutKeyAsyncAction
        .run(() => super.setTestWithoutKey(val));
  }

  late final _$setJobRequestStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setJobRequestStatus',
      context: context);

  @override
  Future<void> setJobRequestStatus(bool val) {
    return _$setJobRequestStatusAsyncAction
        .run(() => super.setJobRequestStatus(val));
  }

  late final _$setBlogStatusAsyncAction =
      AsyncAction('_AppConfigurationStore.setBlogStatus', context: context);

  @override
  Future<void> setBlogStatus(bool val) {
    return _$setBlogStatusAsyncAction.run(() => super.setBlogStatus(val));
  }

  late final _$setSocialLoginStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setSocialLoginStatus',
      context: context);

  @override
  Future<void> setSocialLoginStatus(bool val) {
    return _$setSocialLoginStatusAsyncAction
        .run(() => super.setSocialLoginStatus(val));
  }

  late final _$setGoogleLoginStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setGoogleLoginStatus',
      context: context);

  @override
  Future<void> setGoogleLoginStatus(bool val) {
    return _$setGoogleLoginStatusAsyncAction
        .run(() => super.setGoogleLoginStatus(val));
  }

  late final _$setAppleLoginStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setAppleLoginStatus',
      context: context);

  @override
  Future<void> setAppleLoginStatus(bool val) {
    return _$setAppleLoginStatusAsyncAction
        .run(() => super.setAppleLoginStatus(val));
  }

  late final _$setOTPLoginStatusAsyncAction =
      AsyncAction('_AppConfigurationStore.setOTPLoginStatus', context: context);

  @override
  Future<void> setOTPLoginStatus(bool val) {
    return _$setOTPLoginStatusAsyncAction
        .run(() => super.setOTPLoginStatus(val));
  }

  late final _$setMaintenanceModeStatusAsyncAction = AsyncAction(
      '_AppConfigurationStore.setMaintenanceModeStatus',
      context: context);

  @override
  Future<void> setMaintenanceModeStatus(bool val) {
    return _$setMaintenanceModeStatusAsyncAction
        .run(() => super.setMaintenanceModeStatus(val));
  }

  late final _$setUserDashboardTypeAsyncAction = AsyncAction(
      '_AppConfigurationStore.setUserDashboardType',
      context: context);

  @override
  Future<void> setUserDashboardType(String val) {
    return _$setUserDashboardTypeAsyncAction
        .run(() => super.setUserDashboardType(val));
  }

  late final _$setISUserAuthorizedAsyncAction = AsyncAction(
      '_AppConfigurationStore.setISUserAuthorized',
      context: context);

  @override
  Future<void> setISUserAuthorized(bool val) {
    return _$setISUserAuthorizedAsyncAction
        .run(() => super.setISUserAuthorized(val));
  }

  late final _$setCancellationChargeAmountAsyncAction = AsyncAction(
      '_AppConfigurationStore.setCancellationChargeAmount',
      context: context);

  @override
  Future<void> setCancellationChargeAmount(num val) {
    return _$setCancellationChargeAmountAsyncAction
        .run(() => super.setCancellationChargeAmount(val));
  }

  late final _$setCancellationChargeHoursAsyncAction = AsyncAction(
      '_AppConfigurationStore.setCancellationChargeHours',
      context: context);

  @override
  Future<void> setCancellationChargeHours(int val) {
    return _$setCancellationChargeHoursAsyncAction
        .run(() => super.setCancellationChargeHours(val));
  }

  late final _$setCancellationChargeAsyncAction = AsyncAction(
      '_AppConfigurationStore.setCancellationCharge',
      context: context);

  @override
  Future<void> setCancellationCharge(bool val) {
    return _$setCancellationChargeAsyncAction
        .run(() => super.setCancellationCharge(val));
  }

  late final _$_AppConfigurationStoreActionController =
      ActionController(name: '_AppConfigurationStore', context: context);

  @override
  void setGoogleMapKey(String val) {
    final _$actionInfo = _$_AppConfigurationStoreActionController.startAction(
        name: '_AppConfigurationStore.setGoogleMapKey');
    try {
      return super.setGoogleMapKey(val);
    } finally {
      _$_AppConfigurationStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setFirebaseKey(String val) {
    final _$actionInfo = _$_AppConfigurationStoreActionController.startAction(
        name: '_AppConfigurationStore.setFirebaseKey');
    try {
      return super.setFirebaseKey(val);
    } finally {
      _$_AppConfigurationStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
priceDecimalPoint: ${priceDecimalPoint},
jobRequestStatus: ${jobRequestStatus},
blogStatus: ${blogStatus},
socialLoginStatus: ${socialLoginStatus},
googleLoginStatus: ${googleLoginStatus},
appleLoginStatus: ${appleLoginStatus},
otpLoginStatus: ${otpLoginStatus},
maintenanceModeStatus: ${maintenanceModeStatus},
chatGPTStatus: ${chatGPTStatus},
testWithoutKey: ${testWithoutKey},
firebaseServerKey: ${firebaseServerKey},
googleMapKey: ${googleMapKey},
inquiryEmail: ${inquiryEmail},
helplineNumber: ${helplineNumber},
currencyPosition: ${currencyPosition},
currencySymbol: ${currencySymbol},
currencyCode: ${currencyCode},
isEnableUserWallet: ${isEnableUserWallet},
isAdvancePaymentAllowed: ${isAdvancePaymentAllowed},
slotServiceStatus: ${slotServiceStatus},
digitalServiceStatus: ${digitalServiceStatus},
servicePackageStatus: ${servicePackageStatus},
serviceAddonStatus: ${serviceAddonStatus},
onlinePaymentStatus: ${onlinePaymentStatus},
privacyPolicy: ${privacyPolicy},
termConditions: ${termConditions},
helpAndSupport: ${helpAndSupport},
refundPolicy: ${refundPolicy},
userDashboardType: ${userDashboardType},
isUserAuthorized: ${isUserAuthorized},
cancellationCharge: ${cancellationCharge},
cancellationChargeAmount: ${cancellationChargeAmount},
cancellationChargeHours: ${cancellationChargeHours},
isPromotionalBanner: ${isPromotionalBanner}
    ''';
  }
}
